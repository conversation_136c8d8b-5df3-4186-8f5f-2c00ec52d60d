const ProvinceForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitProvince();
        _componentInputFormatting();
    };

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Initialize
        const validator = $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            highlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            unhighlight: function (element, errorClass) {
                $(element).removeClass(errorClass);
            },
            // Different components require proper error label placement
            errorPlacement: function (error, element) {

                // Input with icons and Select2
                if (element.hasClass('ckeditor')) {
                    error.appendTo(element.parent());
                }

                // Input with icons and Select2
                else if (element.hasClass('select')) {
                    error.appendTo(element.parent());
                }

                // Input group, form checks and custom controls
                else if (element.parents().hasClass('form-control-feedback') || element.parents().hasClass('form-check') || element.parents().hasClass('input-group')) {
                    error.appendTo(element.parent().parent());
                }

                // Other elements
                else {
                    error.insertAfter(element);
                }
            }
        });

        // custom validation rules for province
        $.validator.addMethod('provinceCode', function (value) {
            return /^[A-Z0-9]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole e numeri.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    const _componentDeleteButton = function () {
        const deleteBtn = document.getElementById('delete-province-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                // Check permission first
                if (!hasPermission('PROVINCE_MANAGEMENT', 'delete')) {
                    showToast('Non hai i permessi per eseguire questa operazione.', 'error');
                    return;
                }

                const provinceId = $(this).data('provinceid');
                if (!provinceId) {
                    showToast('Errore: ID provincia non trovato', 'error');
                    return;
                }
                // Show confirmation dialog
                $.confirm({
                    title: 'Conferma eliminazione',
                    content: 'Sei sicuro di voler eliminare questa provincia? Questa azione non può essere annullata.',
                    type: 'red',
                    typeAnimated: true,
                    buttons: {
                        elimina: {
                            text: 'Elimina',
                            btnClass: 'btn-red',
                            action: function () {
                                if (provinceId) {
                                    // Call delete operation
                                    const formData = new FormData();
                                    formData.append('provinceIds', provinceId);
                                    formData.append('operation', 'delete');

                                    $.blockUI();
                                    $.ajax({
                                        url: appRoutes.get('BE_PROVINCE_OPERATE'),
                                        type: 'POST',
                                        data: formData,
                                        processData: false,
                                        contentType: false,
                                        success: function(response) {
                                            $.unblockUI();

                                            // Close offcanvas
                                            const offcanvasElement = deleteBtn.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                                            if (offcanvasElement) {
                                                const overlay = HSOverlay.getInstance(offcanvasElement, true);
                                                if (overlay) {
                                                    overlay.element.close();
                                                }
                                            }

                                            // Reload table
                                            if (window.provincesDataTable && window.provincesDataTable.dataTable) {
                                                window.provincesDataTable.dataTable.ajax.reload();
                                            }

                                            // Show success message
                                            showToast('Provincia eliminata correttamente', 'success');
                                        },
                                        error: function(xhr, status, error) {
                                            $.unblockUI();
                                            let errorMessage = 'Errore durante l\'eliminazione';
                                            if (xhr.responseText) {
                                                errorMessage = xhr.responseText;
                                            }
                                            showToast(errorMessage, 'error');
                                            console.error('Error during province deletion:', error);
                                        }
                                    });
                                }
                            }
                        },
                        annulla: {
                            text: 'Annulla',
                            btnClass: 'btn-light'
                        }
                    }
                });
            });
        }
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('PROVINCE_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#province-edit input, #province-edit textarea, #province-edit select').prop('readonly', true);
            $('#province-edit select').prop('disabled', true);
            $('#province-edit-offcanvas input, #province-edit-offcanvas textarea, #province-edit-offcanvas select').prop('readonly', true);
            $('#province-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#province-edit, #province-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new province forms
        const isNewProvince = !$('#province-edit input[name="id"]').val() && !$('#province-edit-offcanvas input[name="id"]').val();
        if (isNewProvince && !hasPermission('PROVINCE_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#province-edit input, #province-edit textarea, #province-edit select').prop('disabled', true);
            $('#province-edit-offcanvas input, #province-edit-offcanvas textarea, #province-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#province-edit, #province-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitProvince = function () {
        var idForm = "province-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewProvince = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewProvince) {
                    if (!hasPermission('PROVINCE_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare province.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('PROVINCE_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare province.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.provincesDataTable && window.provincesDataTable.dataTable) {
                                window.provincesDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Provincia salvata correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Provincia salvata, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during province save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        $('input[name="code"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Description field - trim whitespace
        $('input[name="description"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    ProvinceForm.init();
});



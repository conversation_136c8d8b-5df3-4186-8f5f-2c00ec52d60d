/**
 * Log functionality for entity activity tracking
 * Handles AJAX loading of entity logs and pagination
 */

/**
 * Load entity logs via AJAX
 * @param {string} entityType - The type of entity (e.g., 'user', 'warranty')
 * @param {string} entityId - The ID of the entity
 * @param {number} skip - Number of records to skip for pagination
 * @param {HTMLElement} container - Container element to load content into
 */
function loadEntityLogs(entityType, entityId, skip, container) {
    if (!container) {
        console.error('loadEntityLogs: Container element is required');
        return;
    }

    // Show loading state if this is the first load
    if (skip === 0) {
        container.innerHTML = `
            <div class="flex justify-center items-center h-32">
                <div class="animate-spin inline-block size-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
                    <span class="sr-only">Loading...</span>
                </div>
            </div>
        `;
    }

    // Make AJAX request to LogController
    $.ajax({
        url: appRoutes.get("BE_ENTITY_LOGS"),
        type: 'GET',
        data: {
            entityType: entityType,
            entityId: entityId,
            skip: skip
        },
        success: function(response) {
            if (skip === 0) {
                // First load - replace content
                container.innerHTML = response;
            } else {
                // Pagination - append content
                appendLogsContent(container, response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading entity logs:', error);
            const errorHtml = `
                <div class="text-center py-8">
                    <div class="mb-4">
                        <svg class="mx-auto size-12 text-red-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="15" x2="9" y1="9" y2="15"/>
                            <line x1="9" x2="15" y1="9" y2="15"/>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Errore di caricamento</h3>
                    <p class="text-sm text-gray-600 dark:text-neutral-400 mt-2">
                        Impossibile caricare l'attività. Riprova più tardi.
                    </p>
                    <button type="button" class="mt-4 py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700" onclick="loadEntityLogs('${entityType}', '${entityId}', 0, this.closest('.p-4'))">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M8 16H3v5"/>
                        </svg>
                        Riprova
                    </button>
                </div>
            `;
            
            if (skip === 0) {
                container.innerHTML = errorHtml;
            } else {
                // For pagination errors, show a toast message
                showToast('Errore nel caricamento di ulteriori attività', 'error');
            }
        }
    });
}

/**
 * Append logs content for pagination
 * @param {HTMLElement} container - Container element
 * @param {string} newContent - New HTML content to append
 */
function appendLogsContent(container, newContent) {
    // Create a temporary container to parse the new content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = newContent;

    // Find the existing main container and the new content
    const existingMainContainer = container.querySelector('div');
    const newMainContainer = tempDiv.querySelector('div');

    if (existingMainContainer && newMainContainer) {
        // Remove the old "Show more" button if it exists
        const oldShowMoreButton = container.querySelector('button[onclick*="loadMoreLogs"]');
        if (oldShowMoreButton) {
            oldShowMoreButton.closest('.relative.group').remove();
        }

        // Append new step items (skip date separators that might already exist)
        const newStepItems = newMainContainer.children;
        const existingStepItems = existingMainContainer.children;

        // Get the last existing date to avoid duplicating date separators
        let lastExistingDate = '';
        for (let i = existingStepItems.length - 1; i >= 0; i--) {
            const stepItem = existingStepItems[i];
            const dateSpan = stepItem.querySelector('span.text-xs.text-gray-500');
            if (dateSpan && dateSpan.textContent.trim().match(/\d{2}\/\d{2}\/\d{4}/)) {
                lastExistingDate = dateSpan.textContent.trim();
                break;
            }
        }

        // Append new items, skipping duplicate date separators
        while (newStepItems.length > 0) {
            const newItem = newStepItems[0];
            const dateSpan = newItem.querySelector('span.text-xs.text-gray-500');
            const isDateSeparator = dateSpan && dateSpan.textContent.trim().match(/\d{2}\/\d{2}\/\d{4}/);

            // Skip date separator if it's the same as the last existing date
            if (isDateSeparator && dateSpan.textContent.trim() === lastExistingDate) {
                newItem.remove();
                continue;
            }

            existingMainContainer.appendChild(newItem);
        }

        // Add the new "Show more" button if it exists
        const newShowMoreButton = tempDiv.querySelector('button[onclick*="loadMoreLogs"]');
        if (newShowMoreButton) {
            existingMainContainer.appendChild(newShowMoreButton.closest('.relative.group'));
        }
    } else {
        // Fallback: replace entire content
        container.innerHTML = newContent;
    }
}

/**
 * Load more logs (called by "Show more" button)
 * @param {string} entityType - The type of entity
 * @param {string} entityId - The ID of the entity
 * @param {number} skip - Number of records to skip
 */
function loadMoreLogs(entityType, entityId, skip) {
    // Find the activity container
    const activityContainers = document.querySelectorAll('[id$="-activity-content"]');
    let container = null;
    
    // Find the active/visible activity container
    for (const activityContainer of activityContainers) {
        const tabContent = activityContainer.closest('[role="tabpanel"]');
        if (tabContent && !tabContent.classList.contains('hidden')) {
            container = activityContainer;
            break;
        }
    }
    
    if (!container) {
        console.error('loadMoreLogs: Could not find active activity container');
        showToast('Errore nel caricamento', 'error');
        return;
    }
    
    // Disable the "Show more" button temporarily
    const showMoreButton = container.querySelector('button[onclick*="loadMoreLogs"]');
    if (showMoreButton) {
        showMoreButton.disabled = true;
        showMoreButton.innerHTML = `
            <div class="animate-spin inline-block size-4 border-[2px] border-current border-t-transparent rounded-full" role="status" aria-label="loading">
                <span class="sr-only">Loading...</span>
            </div>
            Caricamento...
        `;
    }
    
    // Load more logs
    loadEntityLogs(entityType, entityId, skip, container);
}

/**
 * Initialize log functionality when DOM is ready
 */
$(document).ready(function() {
    // Log functionality is initialized via setupActivityTab in custom.js
    // This file provides the supporting functions
    console.log('Log.js loaded successfully');
});

/**
 * Utility function to show toast messages
 * Falls back to alert if showToast is not available
 * @param {string} message - Message to display
 * @param {string} type - Type of message ('success', 'error', 'info')
 */
function showLogToast(message, type) {
    if (typeof showToast === 'function') {
        showToast(message, type);
    } else {
        // Fallback to alert if showToast is not available
        alert(message);
    }
}

/**
 * Format date for display in logs
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
function formatLogDate(date) {
    try {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        return dateObj.toLocaleDateString('it-IT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return 'Data non valida';
    }
}

/**
 * Highlight changed fields in log entries
 * @param {HTMLElement} logEntry - The log entry element
 */
function highlightChangedFields(logEntry) {
    const fieldChanges = logEntry.querySelectorAll('[data-field-change]');
    fieldChanges.forEach(fieldChange => {
        fieldChange.addEventListener('mouseenter', function() {
            this.classList.add('bg-yellow-50', 'dark:bg-yellow-900/20');
        });
        
        fieldChange.addEventListener('mouseleave', function() {
            this.classList.remove('bg-yellow-50', 'dark:bg-yellow-900/20');
        });
    });
}

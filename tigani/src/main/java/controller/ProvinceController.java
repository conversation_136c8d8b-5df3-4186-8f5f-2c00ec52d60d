package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class ProvinceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProvinceController.class.getName());

    public static TemplateViewRoute be_province_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PROVINCE_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_PROVINCE_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_province = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PROVINCE_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("provinceId"));
        if (oid != null) {
            Province loadedProvince = BaseDao.getDocumentById(oid, Province.class);
            attributes.put("curProvince", loadedProvince);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Province loadedProvince = BaseDao.getDocumentByParentId(parentId, Province.class);
                if (loadedProvince != null) {
                    attributes.put("curProvince", loadedProvince);
                }
            }
        }

        return Core.render(Pages.BE_PROVINCE, attributes, request);
    };

    public static TemplateViewRoute be_province_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PROVINCE_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("provinceId"));
        if (oid != null) {
            Province loadedProvince = BaseDao.getDocumentById(oid, Province.class);
            attributes.put("curProvince", loadedProvince);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Province loadedProvince = BaseDao.getDocumentByParentId(parentId, Province.class);
                if (loadedProvince != null) {
                    attributes.put("curProvince", loadedProvince);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_PROVINCE_FORM, attributes, request);
    };

    public static Route be_province_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PROVINCE_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Province> loadedProvinces;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedProvinces = BaseDao.getDocumentsByFilters(Province.class, queryOptions, loadArchived);
        } else {
            loadedProvinces = BaseDao.getDocumentsByFilters(Province.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedProvinces.isEmpty()) {
            for (Province tmpProvince : loadedProvinces) {
                List<String> row = new ArrayList<>();
                row.add(tmpProvince.getId().toString()); // ID for row identification

                // Description con link
                String descriptionLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' provinceId='" +
                    tmpProvince.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpProvince.getDescription(), "N.D.") + "</a>";
                row.add(descriptionLink);

                row.add(StringUtils.defaultIfBlank(tmpProvince.getCode(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpProvince.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpProvince.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_province_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("provinceId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PROVINCE_MANAGEMENT.getCode(), requiredPermission);

        Province newProvince;
        if (oid != null) {
            newProvince = BaseDao.getDocumentById(oid, Province.class);
            RequestUtils.mergeFromParams(params, newProvince);
        } else {
            newProvince = RequestUtils.createFromParams(params, Province.class);
        }

        if (newProvince != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newProvince);
                newProvince.setId(oid);

                BaseDao.insertLog(user, newProvince, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newProvince);
                BaseDao.insertLog(user, newProvince, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_province_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.PROVINCE_MANAGEMENT.getCode(), requiredPermission);

        String provinceIds = params.get("provinceIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(provinceIds)) {
            String[] ids = provinceIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Province tmpProvince = BaseDao.getDocumentById(oid, Province.class);
                    if (tmpProvince != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpProvince);
                                BaseDao.insertLog(user, tmpProvince, LogType.DELETE);
                                break;
                            case "archive":
                                tmpProvince.setArchived(true);
                                BaseDao.updateDocument(tmpProvince);
                                BaseDao.insertLog(user, tmpProvince, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpProvince.setArchived(false);
                                BaseDao.updateDocument(tmpProvince);
                                BaseDao.insertLog(user, tmpProvince, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}

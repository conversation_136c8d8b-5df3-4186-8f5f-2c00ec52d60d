package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.ContactType;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class ContactController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContactController.class.getName());

    public static TemplateViewRoute be_contact_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CONTACT_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_CONTACT_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_contact = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CONTACT_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("contactId"));
        if (oid != null) {
            Contact loadedContact = BaseDao.getDocumentById(oid, Contact.class);
            attributes.put("curContact", loadedContact);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Contact loadedContact = BaseDao.getDocumentByParentId(parentId, Contact.class);
                if (loadedContact != null) {
                    attributes.put("curContact", loadedContact);
                }
            }
        }

        return Core.render(Pages.BE_CONTACT, attributes, request);
    };

    public static TemplateViewRoute be_contact_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CONTACT_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("contactId"));
        if (oid != null) {
            Contact loadedContact = BaseDao.getDocumentById(oid, Contact.class);
            attributes.put("curContact", loadedContact);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Contact loadedContact = BaseDao.getDocumentByParentId(parentId, Contact.class);
                if (loadedContact != null) {
                    attributes.put("curContact", loadedContact);
                }
            }
        }

        // Load all users for userIds select
        List<User> allUsers = BaseDao.getDocumentsByClass(User.class);
        attributes.put("allUsers", allUsers);

        // Return only the form content without the base template
        return Core.render(Pages.BE_CONTACT_FORM, attributes, request);
    };

    public static Route be_contact_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CONTACT_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Contact> loadedContacts;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedContacts = BaseDao.getDocumentsByFilters(Contact.class, queryOptions, loadArchived);
        } else {
            loadedContacts = BaseDao.getDocumentsByFilters(Contact.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedContacts.isEmpty()) {
            for (Contact tmpContact : loadedContacts) {
                List<String> row = new ArrayList<>();
                row.add(""); // prima colonna vuota per checkbox

                // Display name based on contact type
                String displayName = "";
                if (StringUtils.equalsIgnoreCase(tmpContact.getContactType(), ContactType.PERSON.name())) {
                    displayName = StringUtils.defaultIfBlank(tmpContact.getFullName(),
                        (StringUtils.defaultIfBlank(tmpContact.getFirstName(), "N.D.") + " " +
                         StringUtils.defaultIfBlank(tmpContact.getLastName(), "")).trim());
                } else if (StringUtils.equalsIgnoreCase(tmpContact.getContactType(), ContactType.COMPANY.name())) {
                    displayName = StringUtils.defaultIfBlank(tmpContact.getCompanyName(), "N.D.");
                }

                // Nome con link
                String nameLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' contactId='" +
                    tmpContact.getId() + "'>" + displayName + "</a>";
                row.add(nameLink);

                row.add(tmpContact.getContactType() != null ? tmpContact.getContactType() : "N.D.");
                row.add(StringUtils.defaultIfBlank(tmpContact.getEmail(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpContact.getPhoneNumber(), "N.D."));
                row.add(tmpContact.getUserIds() != null ? String.valueOf(tmpContact.getUserIds().size()) : "0");
                row.add(DateTimeUtils.dateToString(tmpContact.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpContact.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_contact_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("contactId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CONTACT_MANAGEMENT.getCode(), requiredPermission);

        Contact newContact;
        if (oid != null) {
            newContact = BaseDao.getDocumentById(oid, Contact.class);
            RequestUtils.mergeFromParams(params, newContact);
        } else {
            newContact = RequestUtils.createFromParams(params, Contact.class);
        }

        if (newContact != null) {
            // Generate fullName for PERSON contacts
            if (StringUtils.equalsIgnoreCase(newContact.getContactType(), ContactType.PERSON.name())) {
                String fullName = (StringUtils.defaultIfBlank(newContact.getFirstName(), "") + " " + 
                                 StringUtils.defaultIfBlank(newContact.getLastName(), "")).trim();
                newContact.setFullName(StringUtils.isNotBlank(fullName) ? fullName : null);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newContact);
                newContact.setId(oid);
                BaseDao.insertLog(user, newContact, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newContact);
                BaseDao.insertLog(user, newContact, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_contact_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CONTACT_MANAGEMENT.getCode(), requiredPermission);

        String contactIds = params.get("contactIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(contactIds)) {
            String[] ids = contactIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Contact tmpContact = BaseDao.getDocumentById(oid, Contact.class);
                    if (tmpContact != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpContact);
                                BaseDao.insertLog(user, tmpContact, LogType.DELETE);
                                break;
                            case "archive":
                                tmpContact.setArchived(true);
                                BaseDao.updateDocument(tmpContact);
                                BaseDao.insertLog(user, tmpContact, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpContact.setArchived(false);
                                BaseDao.updateDocument(tmpContact);
                                BaseDao.insertLog(user, tmpContact, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}

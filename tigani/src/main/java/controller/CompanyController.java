package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.ProfileType;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Company;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class CompanyController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompanyController.class.getName());

    public static TemplateViewRoute be_company = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Company company = BaseDao.getDocumentByClass(Company.class);
        attributes.put("company", company);

        return Core.render(Pages.BE_SETTINGS_COMPANY, attributes, request);
    };

    public static Route be_company_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("companyId"));
        Company newCompany;
        if (oid != null) {
            newCompany = BaseDao.getDocumentById(oid, Company.class);
            RequestUtils.mergeFromParams(params, newCompany);
        } else {
            newCompany = RequestUtils.createFromParams(params, Company.class);
        }

        if (newCompany != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newCompany);
                newCompany.setId(oid);
            } else {
                BaseDao.updateDocument(newCompany);
            }
        }
        if (oid != null) {
            if (!files.isEmpty()) {
                BaseDao.deleteImage(newCompany, "logoImageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newCompany, "logoImageId", true);
            }
        }

        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_SETTINGS_COMPANY_SAVE);
        return null;
    };
}

package controller;

import core.Core;
import dao.BaseDao;
import enums.ProfileType;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.User;
import service.FieldTranslationService;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 * Controller for handling entity activity logs
 * 
 * <AUTHOR>
 */
public class LogController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogController.class.getName());
    private static final int LOGS_PER_PAGE = 5;

    /**
     * Generic route to get activity logs for any entity type
     * Parameters: entityType, entityId, skip (optional, default 0)
     */
    public static Route be_entity_logs = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        try {
            // Get parameters
            String entityType = request.queryParams("entityType");
            String entityIdStr = request.queryParams("entityId");
            String skipStr = request.queryParams("skip");

            // Validate required parameters
            if (StringUtils.isBlank(entityType) || StringUtils.isBlank(entityIdStr)) {
                response.status(400);
                return generateErrorHtml("Parametri mancanti: entityType e entityId sono richiesti");
            }

            // Parse entityId
            ObjectId entityId;
            try {
                entityId = new ObjectId(entityIdStr);
            } catch (IllegalArgumentException e) {
                response.status(400);
                return generateErrorHtml("ID entità non valido");
            }

            // Parse skip parameter
            int skip = 0;
            if (StringUtils.isNotBlank(skipStr)) {
                try {
                    skip = Integer.parseInt(skipStr);
                } catch (NumberFormatException e) {
                    skip = 0;
                }
            }

            // Get log documents
            List<Document> logs = BaseDao.getLogDocuments(entityId, entityType, skip, LOGS_PER_PAGE);
            long totalLogs = BaseDao.countLogDocuments(entityId, entityType);
            
            // Generate HTML response
            return generateLogsHtml(logs, skip, totalLogs, entityType, entityIdStr);

        } catch (Exception ex) {
            LOGGER.error("Error retrieving entity logs", ex);
            response.status(500);
            return generateErrorHtml("Errore interno del server");
        }
    };

    /**
     * Generate HTML for log entries
     */
    private static String generateLogsHtml(List<Document> logs, int skip, long totalLogs, String entityType, String entityId) {
        StringBuilder html = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

        if (logs.isEmpty() && skip == 0) {
            html.append("<div class=\"text-center py-8\">");
            html.append("<div class=\"mb-4\">");
            html.append("<svg class=\"mx-auto size-12 text-gray-400\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
            html.append("<path d=\"M3 3v5h5\"/><path d=\"M3.05 13A9 9 0 1 0 6 5.3L3 8\"/><path d=\"m12 7 2 5-2 1\"/>");
            html.append("</svg>");
            html.append("</div>");
            html.append("<h3 class=\"text-lg font-semibold text-gray-800 dark:text-white\">Nessuna attività</h3>");
            html.append("<p class=\"text-sm text-gray-600 dark:text-neutral-400 mt-2\">Non ci sono ancora modifiche registrate per questo elemento.</p>");
            html.append("</div>");
            return html.toString();
        }

        // Vertical Steps container
        html.append("<div>");
        
        String currentDate = "";
        boolean isFirstEntry = true;

        for (int i = 0; i < logs.size(); i++) {
            Document log = logs.get(i);
            String operationType = log.getString("operationType");
            Date creationDate = log.getDate("creationDate");
            String logDate = creationDate != null ? dateFormat.format(creationDate) : "";
            String logTime = creationDate != null ? timeFormat.format(creationDate) : "";

            // Check if we need a date separator
            if (!logDate.equals(currentDate)) {
                currentDate = logDate;

                // Add date separator
                html.append("<!-- Step Item -->");
                html.append("<div class=\"relative group pb-1 after:absolute after:top-0 first:after:top-4 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600\">");
                html.append("<div class=\"w-full flex gap-x-4\">");
                html.append("<span class=\"relative z-1 flex shrink-0 justify-center items-center w-8\">");
                html.append("<span class=\"flex shrink-0 justify-center items-center size-2 bg-white border border-gray-300 rounded-full dark:bg-neutral-900 dark:border-neutral-600\"></span>");
                html.append("</span>");
                html.append("<div class=\"grow\">");
                html.append("<span class=\"text-xs text-gray-500 dark:text-neutral-500\">");
                html.append(logDate);
                html.append("</span>");
                html.append("</div>");
                html.append("</div>");
                html.append("</div>");
                html.append("<!-- End Step Item -->");

                isFirstEntry = false;
            }

            // Determine if this is the last item in the current group
            boolean isLastInGroup = (i == logs.size() - 1) ||
                (i + 1 < logs.size() && !logDate.equals(logs.get(i + 1).getDate("creationDate") != null ?
                    dateFormat.format(logs.get(i + 1).getDate("creationDate")) : ""));

            String afterClasses = isLastInGroup ?
                "after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700" :
                "after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:bg-gray-300 after:-translate-x-1/2 dark:after:bg-neutral-600";

            html.append("<!-- Step Item -->");
            html.append("<div class=\"relative group last:pb-0 pb-10 ").append(afterClasses).append("\">");
            html.append("<div class=\"w-full flex gap-x-4\">");

            // Icon based on operation type
            String iconHtml = getOperationIcon(operationType);
            html.append(iconHtml);

            // Content
            html.append("<div class=\"grow mt-1\">");
            html.append("<div class=\"flex flex-col gap-y-3\">");
            html.append("<span class=\"text-sm flex flex-wrap items-center gap-1\">");
            html.append("<span class=\"font-medium text-gray-800 dark:text-neutral-200\">Sistema</span>");
            html.append("<span class=\"text-gray-500 dark:text-neutral-500\">").append(getOperationDescription(operationType)).append("</span>");

            // Add time
            html.append("<span class=\"ms-4 relative text-xs text-gray-800 before:absolute before:top-1/2 before:-start-3.5 before:size-1 before:bg-gray-300 before:rounded-full before:translate-x-1/2 before:-translate-y-1/2 before:z-10 dark:text-neutral-200 dark:before:bg-neutral-700\">");
            html.append(logTime);
            html.append("</span>");
            html.append("</span>");

            // Show changed fields if available
            Document updateDescription = (Document) log.get("updateDescription");
            if (updateDescription != null) {
                Document fieldChanges = (Document) updateDescription.get("fieldChanges");
                if (fieldChanges != null && !fieldChanges.isEmpty()) {
                    html.append("<div class=\"flex flex-col gap-y-1\">");

                    for (String fieldName : fieldChanges.keySet()) {
                        Document fieldChange = (Document) fieldChanges.get(fieldName);
                        String displayName = getFieldDisplayName(fieldName);

                        html.append("<div class=\"py-0.5 flex items-center gap-x-2\">");
                        html.append("<span class=\"flex items-center gap-x-2.5 text-xs text-gray-600 dark:text-neutral-400\">");
                        html.append("<span class=\"size-5 flex shrink-0 justify-center items-center\">");
                        if (fieldChange.getBoolean("added", false)) {
                            html.append("<svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
                            html.append("<path d=\"M5 12h14\"></path><path d=\"M12 5v14\"></path>");
                            html.append("</svg>");
                        } else if (fieldChange.getBoolean("removed", false)) {
                            html.append("<svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
                            html.append("<path d=\"M3 6h18\"></path><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"></path><path d=\"M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2\"></path><line x1=\"10\" x2=\"10\" y1=\"11\" y2=\"17\"></line><line x1=\"14\" x2=\"14\" y1=\"11\" y2=\"17\"></line>");
                            html.append("</svg>");
                        } else {
                            html.append("<svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
                            html.append("<path d=\"M12 20h9\"/><path d=\"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"/>");
                            html.append("</svg>");
                        }
                        html.append("</span>");
                        html.append(displayName);
                        html.append("</span>");
                        html.append("<svg class=\"shrink-0 size-3 text-gray-600 dark:text-neutral-400\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
                        html.append("<path d=\"M5 12h14\"/><path d=\"m12 5 7 7-7 7\"/>");
                        html.append("</svg>");

                        Object previousValue = fieldChange.get("previous");
                        Object newValue = fieldChange.get("new");

                        html.append("<span class=\"text-sm text-gray-800 dark:text-neutral-200\">");
                        if (fieldChange.getBoolean("added", false)) {
                            html.append("<span class=\"text-green-600 dark:text-green-400\">").append(formatValue(newValue, fieldName)).append("</span>");
                        } else if (fieldChange.getBoolean("removed", false)) {
                            html.append("<span class=\"text-red-600 dark:text-red-400 line-through\">").append(formatValue(previousValue, fieldName)).append("</span>");
                        } else {
                            html.append("<span class=\"text-gray-500 line-through\">").append(formatValue(previousValue, fieldName)).append("</span>");
                            html.append(" → ");
                            html.append("<span class=\"text-blue-600 dark:text-blue-400\">").append(formatValue(newValue, fieldName)).append("</span>");
                        }
                        html.append("</span>");
                        html.append("</div>");
                    }
                    html.append("</div>");
                }
            }

            html.append("</div>");
            html.append("</div>");
            html.append("</div>");
            html.append("</div>");
            html.append("<!-- End Step Item -->");
        }
        
        // Show more button if there are more logs
        boolean hasMore = (skip + LOGS_PER_PAGE) < totalLogs;
        if (hasMore) {
            html.append("<div class=\"relative group last:pb-0 pb-10 after:absolute after:top-8 after:bottom-0 after:start-4 after:w-px after:border-s after:border-dashed after:border-gray-200 after:-translate-x-1/2 dark:after:border-neutral-700\">");
            html.append("<button type=\"button\" class=\"inline-flex items-center gap-x-4 text-sm text-purple-600 decoration-2 hover:underline focus:outline-hidden focus:underline dark:text-purple-400 dark:hover:text-purple-500\" ");
            html.append("onclick=\"loadMoreLogs('").append(entityType).append("', '").append(entityId).append("', ").append(skip + LOGS_PER_PAGE).append(")\">");
            html.append("<span class=\"flex shrink-0 justify-center items-center size-8 border border-gray-200 text-gray-800 rounded-full dark:border-neutral-700 dark:text-neutral-400\">");
            html.append("<svg class=\"shrink-0 size-3.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">");
            html.append("<path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/>");
            html.append("</svg>");
            html.append("</span>");
            html.append("<span class=\"grow\">");
            html.append("<span>Mostra altri</span>");
            html.append("</span>");
            html.append("</button>");
            html.append("</div>");
        }

        html.append("</div>");
        
        return html.toString();
    }

    /**
     * Get icon HTML for operation type
     */
    private static String getOperationIcon(String operationType) {
        String iconClass = "shrink-0 size-3.5";

        // Default icon (checklist)
        return "<span class=\"flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500\">" +
                "<svg class=\"" + iconClass + "\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">" +
                "<rect x=\"3\" y=\"5\" width=\"6\" height=\"6\" rx=\"1\"/><path d=\"m3 17 2 2 4-4\"/><path d=\"M13 6h8\"/><path d=\"M13 12h8\"/><path d=\"M13 18h8\"/>" +
                "</svg></span>";

        /*if ("update".equals(operationType) || "replace".equals(operationType)) {
            // Edit icon (pencil)
            return "<span class=\"flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500\">" +
                   "<svg class=\"" + iconClass + "\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">" +
                   "<path d=\"M12 20h9\"/><path d=\"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"/>" +
                   "</svg></span>";
        } else if ("delete".equals(operationType)) {
            // Delete icon (trash bin)
            return "<span class=\"flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500\">" +
                   "<svg class=\"" + iconClass + "\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">" +
                   "<path d=\"M3 6h18\"/><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/><path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"/><line x1=\"10\" x2=\"10\" y1=\"11\" y2=\"17\"/><line x1=\"14\" x2=\"14\" y1=\"11\" y2=\"17\"/>" +
                   "</svg></span>";
        } else if ("insert".equals(operationType)) {
            // Insert icon (plus)
            return "<span class=\"flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500\">" +
                   "<svg class=\"" + iconClass + "\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">" +
                   "<path d=\"M5 12h14\"/><path d=\"M12 5v14\"/>" +
                   "</svg></span>";
        } else {
            // Default icon (checklist)
            return "<span class=\"flex shrink-0 justify-center items-center size-8 bg-gray-100 text-sm font-semibold uppercase text-gray-500 rounded-full dark:bg-neutral-800 dark:text-neutral-500\">" +
                   "<svg class=\"" + iconClass + "\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">" +
                   "<rect x=\"3\" y=\"5\" width=\"6\" height=\"6\" rx=\"1\"/><path d=\"m3 17 2 2 4-4\"/><path d=\"M13 6h8\"/><path d=\"M13 12h8\"/><path d=\"M13 18h8\"/>" +
                   "</svg></span>";
        }*/
    }

    /**
     * Format value for display
     */
    private static String formatValue(Object value) {
        if (value == null) {
            return "null";
        }
        String str = value.toString();
        return str.length() > 50 ? str.substring(0, 47) + "..." : str;
    }

    /**
     * Format value for display with field-specific formatting
     */
    private static String formatValue(Object value, String fieldName) {
        if (value == null) {
            return "null";
        }

        // Check if this is a date field
        if (isDateField(fieldName, value)) {
            return formatDateValue(value);
        }

        String str = value.toString();
        return str.length() > 50 ? str.substring(0, 47) + "..." : str;
    }

    /**
     * Check if a field is a date field based on name or value
     */
    private static boolean isDateField(String fieldName, Object value) {
        if (fieldName == null) return false;

        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("date") ||
               lowerFieldName.contains("time") ||
               lowerFieldName.contains("created") ||
               lowerFieldName.contains("updated") ||
               lowerFieldName.contains("modified") ||
               (value instanceof java.util.Date);
    }

    /**
     * Format date value for display
     */
    private static String formatDateValue(Object value) {
        try {
            if (value instanceof java.util.Date) {
                SimpleDateFormat displayFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                return displayFormat.format((java.util.Date) value);
            } else if (value instanceof String) {
                // Try to parse as ISO date string
                String dateStr = value.toString();
                if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}.*")) {
                    SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                    SimpleDateFormat displayFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                    try {
                        java.util.Date date = isoFormat.parse(dateStr);
                        return displayFormat.format(date);
                    } catch (Exception e) {
                        // If parsing fails, return original string
                        return dateStr;
                    }
                }
            }
        } catch (Exception e) {
            // If any error occurs, return original string
        }
        return value.toString();
    }

    /**
     * Get description for operation type
     */
    private static String getOperationDescription(String operationType) {
        switch (operationType) {
            case "update":
                return "ha modificato";
            case "replace":
                return "ha sostituito";
            case "insert":
                return "ha creato";
            case "delete":
                return "ha eliminato";
            default:
                return "ha eseguito operazione: " + operationType;
        }
    }

    /**
     * Get display name for a field using FieldTranslationService
     * Falls back to original field name if translation is not available
     */
    private static String getFieldDisplayName(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return fieldName;
        }

        try {
            // Use FieldTranslationService to get the Italian translation
            String translation = FieldTranslationService.getTranslation(fieldName);

            // If translation is the same as field name, it means no translation was found
            // In this case, try to make the field name more readable
            if (translation.equals(fieldName)) {
                return makeFieldNameReadable(fieldName);
            }

            return translation;
        } catch (Exception e) {
            LOGGER.warn("Error getting translation for field '{}': {}", fieldName, e.getMessage());
            return makeFieldNameReadable(fieldName);
        }
    }

    /**
     * Make field name more readable by converting camelCase to readable format
     */
    private static String makeFieldNameReadable(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return fieldName;
        }

        // Convert camelCase to readable format
        // e.g., "claimNumber" -> "Claim Number"
        String readable = fieldName.replaceAll("([a-z])([A-Z])", "$1 $2");

        // Capitalize first letter
        if (readable.length() > 0) {
            readable = Character.toUpperCase(readable.charAt(0)) + readable.substring(1);
        }

        return readable;
    }

    /**
     * Generate error HTML
     */
    private static String generateErrorHtml(String message) {
        return "<div class=\"text-center py-8\">" +
               "<div class=\"mb-4\">" +
               "<svg class=\"mx-auto size-12 text-red-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">" +
               "<circle cx=\"12\" cy=\"12\" r=\"10\"/><line x1=\"15\" x2=\"9\" y1=\"9\" y2=\"15\"/><line x1=\"9\" x2=\"15\" y1=\"9\" y2=\"15\"/>" +
               "</svg>" +
               "</div>" +
               "<h3 class=\"text-lg font-semibold text-gray-800 dark:text-white\">Errore</h3>" +
               "<p class=\"text-sm text-gray-600 dark:text-neutral-400 mt-2\">" + message + "</p>" +
               "</div>";
    }
}

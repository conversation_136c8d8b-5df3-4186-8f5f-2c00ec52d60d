package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.WarrantyType;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class WarrantyTypeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarrantyTypeController.class.getName());

    public static TemplateViewRoute be_warrantytype_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_WARRANTYTYPE_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_warrantytype = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyTypeId"));
        if (oid != null) {
            WarrantyType loadedWarrantyType = BaseDao.getDocumentById(oid, WarrantyType.class);
            attributes.put("curWarrantyType", loadedWarrantyType);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                WarrantyType loadedWarrantyType = BaseDao.getDocumentByParentId(parentId, WarrantyType.class);
                if (loadedWarrantyType != null) {
                    attributes.put("curWarrantyType", loadedWarrantyType);
                }
            }
        }

        return Core.render(Pages.BE_WARRANTYTYPE, attributes, request);
    };

    public static Route be_warrantytype_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<WarrantyType> loadedWarrantyTypes;
        List<Bson> filters = new ArrayList<>();

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedWarrantyTypes = BaseDao.getDocumentsByFilters(WarrantyType.class, queryOptions, loadArchived);
        } else {
            loadedWarrantyTypes = BaseDao.getDocumentsByFilters(WarrantyType.class, queryOptions);
        }
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedWarrantyTypes.isEmpty()) {
            for (WarrantyType tmpWarrantyType : loadedWarrantyTypes) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' warrantyTypeId='").append(tmpWarrantyType.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_WARRANTYTYPE).append("?warrantyTypeId=").append(tmpWarrantyType.getId()).append("'>").append(StringUtils.defaultIfBlank(tmpWarrantyType.getName(), "N.D.")).append("</a>\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpWarrantyType.getCode(), "N.D.")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpWarrantyType.getIcon(), "N.D.")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpWarrantyType.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpWarrantyType.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_warrantytype_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("warrantyTypeId"));
        WarrantyType newWarrantyType;
        if (oid != null) {
            newWarrantyType = BaseDao.getDocumentById(oid, WarrantyType.class);
            RequestUtils.mergeFromParams(params, newWarrantyType);
        } else {
            newWarrantyType = RequestUtils.createFromParams(params, WarrantyType.class);
        }

        if (newWarrantyType != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newWarrantyType);
                newWarrantyType.setId(oid);

                BaseDao.insertLog(user, newWarrantyType, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newWarrantyType);
                BaseDao.insertLog(user, newWarrantyType, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImage(newWarrantyType, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newWarrantyType, "imageId", true);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_warrantytype_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String warrantyTypeIds = params.get("warrantyTypeIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(warrantyTypeIds)) {
            String[] ids = warrantyTypeIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    WarrantyType tmpWarrantyType = BaseDao.getDocumentById(oid, WarrantyType.class);
                    if (tmpWarrantyType != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpWarrantyType);
                                BaseDao.insertLog(user, tmpWarrantyType, LogType.DELETE);
                                break;
                            case "archive":
                                tmpWarrantyType.setArchived(true);
                                BaseDao.updateDocument(tmpWarrantyType);
                                BaseDao.insertLog(user, tmpWarrantyType, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpWarrantyType.setArchived(false);
                                BaseDao.updateDocument(tmpWarrantyType);
                                BaseDao.insertLog(user, tmpWarrantyType, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}

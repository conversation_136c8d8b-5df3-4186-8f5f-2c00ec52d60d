package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.InsuranceProvenanceType;
import pojo.User;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 *
 * <AUTHOR>
 */
public class InsuranceProvenanceTypeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(InsuranceProvenanceTypeController.class.getName());

    public static TemplateViewRoute be_insuranceprovenancetype_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_INSURANCEPROVENANCETYPE_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_insuranceprovenancetype = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("insuranceProvenanceTypeId"));
        if (oid != null) {
            InsuranceProvenanceType loadedInsuranceProvenanceType = BaseDao.getDocumentById(oid, InsuranceProvenanceType.class);
            attributes.put("curInsuranceProvenanceType", loadedInsuranceProvenanceType);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                InsuranceProvenanceType loadedInsuranceProvenanceType = BaseDao.getDocumentByParentId(parentId, InsuranceProvenanceType.class);
                if (loadedInsuranceProvenanceType != null) {
                    attributes.put("curInsuranceProvenanceType", loadedInsuranceProvenanceType);
                }
            }
        }

        return Core.render(Pages.BE_INSURANCEPROVENANCETYPE, attributes, request);
    };

    public static Route be_insuranceprovenancetype_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<InsuranceProvenanceType> loadedInsuranceProvenanceTypes;
        List<Bson> filters = new ArrayList<>();

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedInsuranceProvenanceTypes = BaseDao.getDocumentsByFilters(InsuranceProvenanceType.class, queryOptions, loadArchived);
        } else {
            loadedInsuranceProvenanceTypes = BaseDao.getDocumentsByFilters(InsuranceProvenanceType.class, queryOptions);
        }
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedInsuranceProvenanceTypes.isEmpty()) {
            for (InsuranceProvenanceType tmpInsuranceProvenanceType : loadedInsuranceProvenanceTypes) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' insuranceProvenanceTypeId='").append(tmpInsuranceProvenanceType.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_INSURANCEPROVENANCETYPE).append("?insuranceProvenanceTypeId=").append(tmpInsuranceProvenanceType.getId()).append("'>").append(StringUtils.defaultIfBlank(tmpInsuranceProvenanceType.getTitle(), "N.D.")).append("</a>\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpInsuranceProvenanceType.getCode(), "N.D.")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpInsuranceProvenanceType.getDescription(), "N.D.")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpInsuranceProvenanceType.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpInsuranceProvenanceType.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_insuranceprovenancetype_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("insuranceProvenanceTypeId"));
        InsuranceProvenanceType newInsuranceProvenanceType;
        if (oid != null) {
            newInsuranceProvenanceType = BaseDao.getDocumentById(oid, InsuranceProvenanceType.class);
            RequestUtils.mergeFromParams(params, newInsuranceProvenanceType);
        } else {
            newInsuranceProvenanceType = RequestUtils.createFromParams(params, InsuranceProvenanceType.class);
        }

        if (newInsuranceProvenanceType != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newInsuranceProvenanceType);
                newInsuranceProvenanceType.setId(oid);

                BaseDao.insertLog(user, newInsuranceProvenanceType, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newInsuranceProvenanceType);
                BaseDao.insertLog(user, newInsuranceProvenanceType, LogType.UPDATE);
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImage(newInsuranceProvenanceType, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), newInsuranceProvenanceType, "imageId", true);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_insuranceprovenancetype_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String insuranceProvenanceTypeIds = params.get("insuranceProvenanceTypeIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(insuranceProvenanceTypeIds)) {
            String[] ids = insuranceProvenanceTypeIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    InsuranceProvenanceType tmpInsuranceProvenanceType = BaseDao.getDocumentById(oid, InsuranceProvenanceType.class);
                    if (tmpInsuranceProvenanceType != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpInsuranceProvenanceType);
                                BaseDao.insertLog(user, tmpInsuranceProvenanceType, LogType.DELETE);
                                break;
                            case "archive":
                                tmpInsuranceProvenanceType.setArchived(true);
                                BaseDao.updateDocument(tmpInsuranceProvenanceType);
                                BaseDao.insertLog(user, tmpInsuranceProvenanceType, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpInsuranceProvenanceType.setArchived(false);
                                BaseDao.updateDocument(tmpInsuranceProvenanceType);
                                BaseDao.insertLog(user, tmpInsuranceProvenanceType, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}

package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.FieldTranslation;
import pojo.User;
import service.FieldTranslationService;
import spark.*;
import utils.*;

import java.util.*;
import org.bson.conversions.Bson;
import pojo.QueryOptions;

/**
 * Controller for managing field translations
 * Provides CRUD operations and management interface for AI-generated field translations
 * 
 * <AUTHOR>
 */
public class FieldTranslationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FieldTranslationController.class.getName());

    public static TemplateViewRoute be_fieldtranslation_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_FIELDTRANSLATION_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_fieldtranslation = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("fieldTranslationId"));
        if (oid != null) {
            FieldTranslation loadedFieldTranslation = BaseDao.getDocumentById(oid, FieldTranslation.class);
            attributes.put("curFieldTranslation", loadedFieldTranslation);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                FieldTranslation loadedFieldTranslation = BaseDao.getDocumentByParentId(parentId, FieldTranslation.class);
                if (loadedFieldTranslation != null) {
                    attributes.put("curFieldTranslation", loadedFieldTranslation);
                }
            }
        }

        return Core.render(Pages.BE_FIELDTRANSLATION, attributes, request);
    };

    public static Route be_fieldtranslation_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        // Build filters
        List<Bson> filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("archived", DaoFiltersOperation.NE, true));

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
        List<FieldTranslation> loadedFieldTranslations = BaseDao.getDocumentsByFilters(FieldTranslation.class, queryOptions);
        
        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedFieldTranslations.isEmpty()) {
            for (FieldTranslation tmpFieldTranslation : loadedFieldTranslations) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota per checkbox
                json.append("\"<a target='_blank' fieldTranslationId='").append(tmpFieldTranslation.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_FIELDTRANSLATION).append("?fieldTranslationId=").append(tmpFieldTranslation.getId()).append("'>").append(StringUtils.defaultIfBlank(tmpFieldTranslation.getFieldName(), "N.D.")).append("</a>\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpFieldTranslation.getTranslation(), "N.D.")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpFieldTranslation.getSourceClass(), "N.D.")).append("\",");
                json.append("\"").append(StringUtils.defaultIfBlank(tmpFieldTranslation.getFieldType(), "N.D.")).append("\",");
                json.append("\"").append(BooleanUtils.isTrue(tmpFieldTranslation.getIsFromBasePojo()) ? "Sì" : "No").append("\",");
                json.append("\"").append(BooleanUtils.isTrue(tmpFieldTranslation.getIsAiGenerated()) ? "AI" : "Manuale").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpFieldTranslation.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(tmpFieldTranslation.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"<div class='d-inline-flex'><a href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_FIELDTRANSLATION).append("?fieldTranslationId=").append(tmpFieldTranslation.getId()).append("' class='btn btn-link btn-sm' title='Modifica'><i class='ph-note-pencil'></i></a></div>\",");
                json.append("\"\""); // ultima colonna vuota per responsive
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovi ultima virgola
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_fieldtranslation_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("fieldTranslationId"));
        FieldTranslation newFieldTranslation;
        if (oid != null) {
            newFieldTranslation = BaseDao.getDocumentById(oid, FieldTranslation.class);
            RequestUtils.mergeFromParams(params, newFieldTranslation);
        } else {
            newFieldTranslation = RequestUtils.createFromParams(params, FieldTranslation.class);
        }

        if (newFieldTranslation != null) {
            // Mark as manually edited if updating
            if (oid != null) {
                newFieldTranslation.setIsAiGenerated(false);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newFieldTranslation);
                newFieldTranslation.setId(oid);

                BaseDao.insertLog(user, newFieldTranslation, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newFieldTranslation);
                BaseDao.insertLog(user, newFieldTranslation, LogType.UPDATE);
                
                // Update the service cache
                FieldTranslationService.refreshCache();
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_fieldtranslation_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        String fieldTranslationIds = params.get("fieldTranslationIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(fieldTranslationIds)) {
            String[] ids = StringUtils.split(fieldTranslationIds, ",");
            List<ObjectId> objectIds = new ArrayList<>();
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    objectIds.add(oid);
                }
            }

            if (!objectIds.isEmpty()) {
                if (operation.equals("delete")) {
                    for (ObjectId oid : objectIds) {
                        FieldTranslation fieldTranslation = BaseDao.getDocumentById(oid, FieldTranslation.class);
                        if (fieldTranslation != null) {
                            BaseDao.deleteDocument(fieldTranslation);
                            BaseDao.insertLog(user, fieldTranslation, LogType.DELETE);
                        }
                    }
                }
                
                // Refresh cache after bulk operations
                FieldTranslationService.refreshCache();
            }
        }

        return "OK";
    };

    /**
     * Regenerate all translations using AI
     */
    public static Route be_fieldtranslation_regenerate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        try {
            FieldTranslationService.regenerateAllTranslations();
            return "OK";
        } catch (Exception e) {
            LOGGER.error("Failed to regenerate translations", e);
            return Spark.halt(500, "Errore durante la rigenerazione delle traduzioni");
        }
    };
}

package service;

import static com.mongodb.MongoClient.getDefaultCodecRegistry;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Filters.eq;

import com.mongodb.client.ChangeStreamIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.changestream.ChangeStreamDocument;
import com.mongodb.client.model.changestream.FullDocument;
import com.mongodb.client.model.changestream.OperationType;
import core.Core;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import dao.BaseDaoBackup;
import org.bson.*;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.BackupDocument;
import utils.Defaults;

/**
 * Service for handling MongoDB Change Streams and creating backups
 * 
 * <AUTHOR>
 */
public class ChangeStreamBackupService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChangeStreamBackupService.class.getName());
    
    private static ExecutorService executorService;
    private static volatile boolean isRunning = false;

    /**
     * Start the change stream monitoring service
     */
    public static void startChangeStreamMonitoring() {
        if (!Defaults.ENABLE_CHANGE_STREAMS_BACKUP) {
            LOGGER.info("Change Streams backup is disabled in configuration");
            return;
        }

        if (isRunning) {
            LOGGER.warn("Change Stream monitoring is already running");
            return;
        }

        if (Defaults.CHANGE_STREAMS_COLLECTIONS == null || Defaults.CHANGE_STREAMS_COLLECTIONS.isEmpty()) {
            LOGGER.warn("No collections configured for Change Streams monitoring");
            return;
        }

        try {
            executorService = Executors.newSingleThreadExecutor(r -> {
                Thread t = new Thread(r, "ChangeStreamBackup");
                t.setDaemon(true);
                return t;
            });

            isRunning = true;
            executorService.submit(() -> {
                try {
                    monitorChangeStreams();
                } catch (Exception ex) {
                    LOGGER.error("Error in change stream monitoring", ex);
                    isRunning = false;
                }
            });

            LOGGER.info("Change Stream backup monitoring started for collections: {}", Defaults.CHANGE_STREAMS_COLLECTIONS);
        } catch (Exception ex) {
            LOGGER.error("Failed to start Change Stream monitoring", ex);
            isRunning = false;
        }
    }

    /**
     * Stop the change stream monitoring service
     */
    public static void stopChangeStreamMonitoring() {
        if (!isRunning) {
            return;
        }

        isRunning = false;
        if (executorService != null) {
            executorService.shutdown();
            LOGGER.info("Change Stream backup monitoring stopped");
        }
    }

    /**
     * Check if the service is currently running
     */
    public static boolean isRunning() {
        return isRunning;
    }

    /**
     * Monitor change streams for configured collections
     */
    private static void monitorChangeStreams() {
        try {
            // Create pipeline to filter only update operations on specified collections
            List<Bson> pipeline = Arrays.asList(
                match(
                    Filters.and(
                        Filters.in("operationType", Arrays.asList("update", "replace")),
                            Filters.in("ns.coll", Defaults.CHANGE_STREAMS_COLLECTIONS.stream().map(String::toLowerCase).collect(Collectors.toList()))
                    )
                )
            );

            // Watch change streams with full document lookup
            ChangeStreamIterable<Document> changeStream = Core.mongoDatabase
                .watch(pipeline)
                .fullDocument(FullDocument.UPDATE_LOOKUP);

            LOGGER.info("Starting to monitor change streams...");

            // Process change stream events
            for (ChangeStreamDocument<Document> changeDoc : changeStream) {
                if (!isRunning) {
                    break;
                }

                try {
                    processChangeStreamEvent(changeDoc);
                } catch (Exception ex) {
                    LOGGER.error("Error processing change stream event", ex);
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Error in change stream monitoring loop", ex);
        } finally {
            isRunning = false;
        }
    }

    /**
     * Process a single change stream event
     */
    private static void processChangeStreamEvent(ChangeStreamDocument<Document> changeDoc) {
        try {
            if (changeDoc.getOperationType() != OperationType.UPDATE && changeDoc.getOperationType() != OperationType.REPLACE) {
                return; // We only handle updates and replacements
            }

            String collectionName = changeDoc.getNamespace().getCollectionName();
            if (Defaults.CHANGE_STREAMS_COLLECTIONS.stream().noneMatch(c -> c.equalsIgnoreCase(collectionName))) {
                return; // Skip collections not in our monitoring list
            }

            // Extract document ID
            BsonDocument documentKey = changeDoc.getDocumentKey();
            if (documentKey == null || !documentKey.containsKey("_id")) {
                LOGGER.warn("Change stream event missing document key");
                return;
            }

            ObjectId realId = documentKey.getObjectId("_id").getValue();

            // Create backup document
            BackupDocument backup = new BackupDocument();
            backup.setRealId(realId);
            // backup.setCollectionName(collectionName);
            backup.setOperationType(changeDoc.getOperationType().getValue());
            backup.setCreationDate(new Date());

            // Set resume token for ordering
            if (changeDoc.getResumeToken() != null) {
                backup.setResumeToken(changeDoc.getResumeToken().toJson());
            }

            // Set full document (after change)
            if (changeDoc.getFullDocument() != null) {
                backup.setFullDocument(changeDoc.getFullDocument().toBsonDocument(Document.class, getDefaultCodecRegistry()));
            }

            // Note: getFullDocumentBeforeChange() is not available in MongoDB Java Driver 3.12
            // This feature was added in later versions

            // Set update description with change details
            if (changeDoc.getUpdateDescription() != null) {
                BsonDocument updateDesc = new BsonDocument();
                
                if (changeDoc.getUpdateDescription().getUpdatedFields() != null) {
                    updateDesc.put("updatedFields", changeDoc.getUpdateDescription().getUpdatedFields());
                }
                
                if (changeDoc.getUpdateDescription().getRemovedFields() != null && 
                    !changeDoc.getUpdateDescription().getRemovedFields().isEmpty()) {
                    updateDesc.put("removedFields", 
                        new BsonArray(changeDoc.getUpdateDescription().getRemovedFields().stream()
                            .map(BsonString::new)
                            .collect(Collectors.toList())));
                }
                
                // Note: getTruncatedArrays() is not available in MongoDB Java Driver 3.12
                // This feature was added in later versions
                
                backup.setUpdateDescription(updateDesc);
            } else if (changeDoc.getOperationType() == OperationType.REPLACE) {
                // MongoDB does not provide updateDescription for REPLACE operations.
                // Compute a minimal diff using the previous backup fullDocument (if any).
                try {
                    BackupDocument prev = BaseDaoBackup.getLatestBackupByRealId(realId, collectionName);
                    BsonDocument beforeDoc = prev != null ? prev.getFullDocument() : null;
                    BsonDocument afterDoc = backup.getFullDocument();
                    if (afterDoc != null) {
                        BsonDocument computed = computeUpdateDescriptionFromDocuments(beforeDoc, afterDoc);
                        if (computed != null && !computed.isEmpty()) {
                            backup.setUpdateDescription(computed);
                        }
                    }
                } catch (Exception ex) {
                    LOGGER.warn("Unable to compute diff for REPLACE operation", ex);
                }
            }

            // Add metadata
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("clusterTime", changeDoc.getClusterTime());
            // Note: getWallTime() is not available in MongoDB Java Driver 3.12
            // This feature was added in later versions
            backup.setMetadata(metadata);

            // Check if this change should be excluded (only lastUpdate field changed)
            if (shouldExcludeChange(backup)) {
                LOGGER.debug("Excluding backup for document {} in collection {} - only lastUpdate field changed", realId, collectionName);
                return;
            }

            // Save backup to database
            saveBackupDocument(backup, collectionName);

            LOGGER.debug("Backup created for document {} in collection {}", realId, collectionName);

        } catch (Exception ex) {
            LOGGER.error("Error processing change stream event", ex);
        }
    }

    /**
     * Save backup document to the backup collection
     */
    private static void saveBackupDocument(BackupDocument backup, String collectionName) {
        try {
            String backupDbName = Defaults.BACKUP_DATABASE_NAME;
            MongoDatabase backupDb = Core.getDatabase(backupDbName);
            MongoCollection<Document> collection = backupDb.getCollection(collectionName);

            Document doc = new Document()
                .append("realId", backup.getRealId())
                .append("resumeToken", backup.getResumeToken())
                .append("operationType", backup.getOperationType())
                .append("creationDate", backup.getCreationDate())
                .append("fullDocument", backup.getFullDocument())
                // Note: fullDocumentBeforeChange is not available in MongoDB Java Driver 3.12
                .append("updateDescription", backup.getUpdateDescription())
                .append("metadata", backup.getMetadata());

            collection.insertOne(doc);

        } catch (Exception ex) {
            LOGGER.error("Error saving backup document", ex);
        }
    }

    /**
     * Get backup collection name
     */
    public static String getBackupCollectionName() {
        return Defaults.BACKUP_DATABASE_NAME;
    }

    /**
     * Compute a detailed updateDescription structure by diffing two full documents.
     * This is used for REPLACE operations where MongoDB does not provide updateDescription.
     * Includes both the standard updateDescription format and detailed field changes with previous/new values.
     * Top-level fields only to keep implementation minimal.
     */
    private static BsonDocument computeUpdateDescriptionFromDocuments(BsonDocument before, BsonDocument after) {
        if (after == null) {
            return null;
        }
        BsonDocument result = new BsonDocument();
        BsonDocument updatedFields = new BsonDocument();
        BsonArray removedFields = new BsonArray();
        BsonDocument fieldChanges = new BsonDocument(); // New: detailed field changes

        if (before == null) {
            // Treat as all fields updated (new document)
            result.put("updatedFields", after);

            // Add detailed field changes for new document
            for (String key : after.keySet()) {
                BsonDocument fieldChange = new BsonDocument();
                fieldChange.put("previous", BsonNull.VALUE);
                fieldChange.put("new", after.get(key));
                fieldChange.put("added", BsonBoolean.TRUE);
                fieldChanges.put(key, fieldChange);
            }
            result.put("fieldChanges", fieldChanges);
            return result;
        }

        // Added or changed fields
        for (String key : after.keySet()) {
            BsonValue afterVal = after.get(key);
            BsonValue beforeVal = before.get(key);
            if (beforeVal == null || !afterVal.equals(beforeVal)) {
                updatedFields.put(key, afterVal);

                // Add detailed field change
                BsonDocument fieldChange = new BsonDocument();
                fieldChange.put("previous", beforeVal != null ? beforeVal : BsonNull.VALUE);
                fieldChange.put("new", afterVal);
                if (beforeVal == null) {
                    fieldChange.put("added", BsonBoolean.TRUE);
                }
                fieldChanges.put(key, fieldChange);
            }
        }

        // Removed fields
        for (String key : before.keySet()) {
            if (!after.containsKey(key)) {
                removedFields.add(new BsonString(key));

                // Add detailed field change for removed field
                BsonDocument fieldChange = new BsonDocument();
                fieldChange.put("previous", before.get(key));
                fieldChange.put("new", BsonNull.VALUE);
                fieldChange.put("removed", BsonBoolean.TRUE);
                fieldChanges.put(key, fieldChange);
            }
        }

        if (!updatedFields.isEmpty()) {
            result.put("updatedFields", updatedFields);
        }
        if (!removedFields.isEmpty()) {
            result.put("removedFields", removedFields);
        }
        if (!fieldChanges.isEmpty()) {
            result.put("fieldChanges", fieldChanges);
        }
        return result;
    }

    /**
     * Check if a backup change should be excluded from logging
     * Excludes changes that only contain the "lastUpdate" field
     *
     * @param backup The backup document to check
     * @return true if the change should be excluded, false otherwise
     */
    private static boolean shouldExcludeChange(BackupDocument backup) {
        if (backup == null || backup.getUpdateDescription() == null) {
            return false;
        }

        BsonDocument updateDesc = backup.getUpdateDescription();

        // Check if there are updated fields
        BsonDocument updatedFields = updateDesc.getDocument("updatedFields", null);
        if (updatedFields != null && !updatedFields.isEmpty()) {
            // If there's only one updated field and it's "lastUpdate", exclude it
            if (updatedFields.size() == 1 && updatedFields.containsKey("lastUpdate")) {
                return true;
            }
        }

        // Check if there are removed fields (if so, don't exclude)
        BsonArray removedFields = updateDesc.getArray("removedFields", null);
        if (removedFields != null && !removedFields.isEmpty()) {
            return false;
        }

        // Check if there are field changes
        BsonDocument fieldChanges = updateDesc.getDocument("fieldChanges", null);
        if (fieldChanges != null && !fieldChanges.isEmpty()) {
            // If there's only one field change and it's "lastUpdate", exclude it
            if (fieldChanges.size() == 1 && fieldChanges.containsKey("lastUpdate")) {
                return true;
            }
        }

        return false;
    }
}

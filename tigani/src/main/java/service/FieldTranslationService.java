package service;

import commons.GeminiCommons;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.FieldTranslation;
import pojo.QueryOptions;
import utils.Defaults;
import utils.ReflectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for generating and managing field translations using Gemini AI
 * Orchestrates field extraction, AI translation generation, and storage/retrieval
 * 
 * <AUTHOR>
 */
public class FieldTranslationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FieldTranslationService.class.getName());

    // In-memory cache for translations
    private static final Map<String, String> translationCache = new ConcurrentHashMap<>();

    private static boolean initialized = false;

    /**
     * Initialize the translation service
     * This method should be called during application startup
     */
    public static void initializeTranslations() {
        if (!Defaults.ENABLE_FIELD_TRANSLATIONS) {
            LOGGER.info("Field translations are disabled in configuration");
            return;
        }

        if (initialized) {
            LOGGER.warn("Field translation service is already initialized");
            return;
        }

        LOGGER.info("Initializing field translation service...");
        long startTime = System.currentTimeMillis();

        try {
            // First, try to load existing translations from database
            loadTranslationsFromDatabase();

            // Extract field names from POJOs
            Set<String> fieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            
            if (fieldNames.isEmpty()) {
                LOGGER.warn("No field names extracted from CHANGE_STREAMS_COLLECTIONS");
                initialized = true;
                return;
            }

            // Check if we need to generate new translations
            Set<String> missingTranslations = findMissingTranslations(fieldNames);
            
            if (!missingTranslations.isEmpty()) {
                LOGGER.info("Found {} fields without translations, generating using Gemini AI", missingTranslations.size());
                generateAndStoreTranslations(missingTranslations);
            } else {
                LOGGER.info("All field translations are already available");
            }

            initialized = true;
            long endTime = System.currentTimeMillis();
            LOGGER.info("Field translation service initialized in {} ms. Total translations: {}", 
                       (endTime - startTime), translationCache.size());

        } catch (Exception e) {
            LOGGER.error("Failed to initialize field translation service", e);
            // Don't throw exception to avoid breaking application startup
        }
    }

    /**
     * Get translation for a specific field name
     * 
     * @param fieldName The field name to translate
     * @return The Italian translation or the original field name if no translation exists
     */
    public static String getTranslation(String fieldName) {
        if (!initialized) {
            LOGGER.warn("Translation service not initialized, returning original field name: {}", fieldName);
            return fieldName;
        }

        return translationCache.getOrDefault(fieldName, fieldName);
    }

    /**
     * Get all available translations
     * 
     * @return Map of field name -> translation
     */
    public static Map<String, String> getAllTranslations() {
        return new HashMap<>(translationCache);
    }

    /**
     * Force regeneration of all translations
     * This method can be called manually if needed
     */
    public static void regenerateAllTranslations() {
        LOGGER.info("Force regenerating all field translations...");
        
        try {
            // Clear existing cache
            translationCache.clear();
            
            // Extract all field names
            Set<String> fieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            
            if (!fieldNames.isEmpty()) {
                generateAndStoreTranslations(fieldNames);
                LOGGER.info("Successfully regenerated {} translations", translationCache.size());
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to regenerate translations", e);
        }
    }

    /**
     * Load existing translations from database
     */
    private static void loadTranslationsFromDatabase() {
        try {
            LOGGER.debug("Loading existing translations from database");

            List<FieldTranslation> existingTranslations = BaseDao.getDocumentsByClass(FieldTranslation.class);

            for (FieldTranslation translation : existingTranslations) {
                if (translation.getFieldName() != null && translation.getTranslation() != null) {
                    translationCache.put(translation.getFieldName(), translation.getTranslation());
                }
            }

            LOGGER.info("Loaded {} existing translations from database", existingTranslations.size());

        } catch (Exception e) {
            LOGGER.warn("Failed to load existing translations from database", e);
        }
    }

    /**
     * Find field names that don't have translations yet
     */
    private static Set<String> findMissingTranslations(Set<String> fieldNames) {
        Set<String> missing = new LinkedHashSet<>();
        
        for (String fieldName : fieldNames) {
            if (!translationCache.containsKey(fieldName)) {
                missing.add(fieldName);
            }
        }
        
        return missing;
    }

    /**
     * Generate translations using Gemini AI and store them
     */
    private static void generateAndStoreTranslations(Set<String> fieldNames) throws IOException {
        if (fieldNames.isEmpty()) {
            return;
        }

        // Get detailed field information for context
        Map<String, List<ReflectionUtils.FieldInfo>> detailedInfo = ReflectionUtils.extractDetailedFieldInfo();

        // Convert to list for API call
        List<String> fieldNamesList = new ArrayList<>(fieldNames);

        // Generate translations using Gemini AI
        Map<String, String> newTranslations = GeminiCommons.generateFieldTranslations(fieldNamesList);

        if (newTranslations.isEmpty()) {
            LOGGER.warn("No translations generated by Gemini AI");
            return;
        }

        // Save to database
        saveTranslationsToDatabase(newTranslations, detailedInfo);

        // Add to cache
        translationCache.putAll(newTranslations);

        LOGGER.info("Generated and stored {} new translations", newTranslations.size());
    }

    /**
     * Save translations to database
     */
    private static void saveTranslationsToDatabase(Map<String, String> translations,
                                                  Map<String, List<ReflectionUtils.FieldInfo>> detailedInfo) {
        try {
            for (Map.Entry<String, String> entry : translations.entrySet()) {
                String fieldName = entry.getKey();
                String translation = entry.getValue();

                // Check if translation already exists
                List<Bson> filters = new ArrayList<>();
                filters.add(DaoFilters.getFilter("fieldName", DaoFiltersOperation.EQ, fieldName));

                QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                FieldTranslation existingTranslation = BaseDao.getDocumentByFilters(FieldTranslation.class, queryOptions);

                if (existingTranslation == null) {
                    // Create new translation
                    FieldTranslation newTranslation = new FieldTranslation();
                    newTranslation.setFieldName(fieldName);
                    newTranslation.setTranslation(translation);
                    newTranslation.setIsAiGenerated(true);

                    // Find field info for additional context
                    ReflectionUtils.FieldInfo fieldInfo = findFieldInfo(fieldName, detailedInfo);
                    if (fieldInfo != null) {
                        newTranslation.setSourceClass(fieldInfo.getDeclaringClass());
                        newTranslation.setFieldType(fieldInfo.getType());
                        newTranslation.setIsFromBasePojo(fieldInfo.isFromBasePojo());
                    }

                    BaseDao.insertDocument(newTranslation);
                    LOGGER.debug("Saved new translation: {} -> {}", fieldName, translation);
                }
            }

            LOGGER.info("Saved {} translations to database", translations.size());

        } catch (Exception e) {
            LOGGER.error("Failed to save translations to database", e);
        }
    }

    /**
     * Find field info for a specific field name
     */
    private static ReflectionUtils.FieldInfo findFieldInfo(String fieldName,
                                                          Map<String, List<ReflectionUtils.FieldInfo>> detailedInfo) {
        for (List<ReflectionUtils.FieldInfo> fieldInfoList : detailedInfo.values()) {
            for (ReflectionUtils.FieldInfo fieldInfo : fieldInfoList) {
                if (fieldName.equals(fieldInfo.getName())) {
                    return fieldInfo;
                }
            }
        }
        return null;
    }

    /**
     * Get translation statistics
     */
    public static Map<String, Object> getTranslationStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized);
        stats.put("totalTranslations", translationCache.size());
        stats.put("translationsEnabled", Defaults.ENABLE_FIELD_TRANSLATIONS);
        
        if (initialized) {
            Set<String> allFieldNames = ReflectionUtils.extractFieldNamesFromChangeStreamCollections();
            stats.put("totalFields", allFieldNames.size());
            stats.put("missingTranslations", findMissingTranslations(allFieldNames).size());
        }
        
        return stats;
    }

    /**
     * Check if the service is initialized
     */
    public static boolean isInitialized() {
        return initialized;
    }

    /**
     * Update a translation manually
     */
    public static void updateTranslation(String fieldName, String newTranslation) {
        try {
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("fieldName", DaoFiltersOperation.EQ, fieldName));

            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
            FieldTranslation existingTranslation = BaseDao.getDocumentByFilters(FieldTranslation.class, queryOptions);

            if (existingTranslation != null) {
                existingTranslation.setTranslation(newTranslation);
                existingTranslation.setIsAiGenerated(false); // Mark as manually edited
                BaseDao.updateDocument(existingTranslation);

                // Update cache
                translationCache.put(fieldName, newTranslation);

                LOGGER.info("Updated translation for field '{}' to '{}'", fieldName, newTranslation);
            } else {
                LOGGER.warn("Translation not found for field: {}", fieldName);
            }

        } catch (Exception e) {
            LOGGER.error("Failed to update translation for field: " + fieldName, e);
        }
    }

    /**
     * Get all field translations from database
     */
    public static List<FieldTranslation> getAllFieldTranslations() {
        try {
            return BaseDao.getDocumentsByClass(FieldTranslation.class);
        } catch (Exception e) {
            LOGGER.error("Failed to get all field translations", e);
            return new ArrayList<>();
        }
    }

    /**
     * Get field translation by field name
     */
    public static FieldTranslation getFieldTranslationByName(String fieldName) {
        try {
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("fieldName", DaoFiltersOperation.EQ, fieldName));

            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
            return BaseDao.getDocumentByFilters(FieldTranslation.class, queryOptions);
        } catch (Exception e) {
            LOGGER.error("Failed to get field translation for: " + fieldName, e);
            return null;
        }
    }

    /**
     * Refresh translation cache from database
     */
    public static void refreshCache() {
        translationCache.clear();
        loadTranslationsFromDatabase();
        LOGGER.info("Translation cache refreshed with {} translations", translationCache.size());
    }
}

package core;

import controller.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Filter;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateEngine;
import spark.TemplateViewRoute;
import spark.servlet.SparkApplication;
import utils.Defaults;
import utils.EnvironmentUtils;
import utils.RoutesUtils;

/**
 *
 * <AUTHOR>
 */
public class Application implements SparkApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class.getName());
    private static boolean reloadApplication = false;

    @Override
    public void init() {
        initApplication();
    }

    public static void reloadApplication() {
        reloadApplication = true;
        initApplication();
        reloadApplication = false;
        Routes.reloadPaths();
        initApplication();
    }

    public static void initApplication() {
        // configure static resources
        // ... remember to put index.html in your static folder if you want a
        //     project default page
        if (EnvironmentUtils.isLocal()) {
            Spark.staticFiles.externalLocation("/projects/" + Defaults.PROJECT_NAME + "/" + Defaults.PROJECT_NAME + "/src/main/resources/public");
            // no caching
        } else {
            Spark.staticFileLocation("/public");
            Spark.staticFiles.expireTime(Defaults.STATIC_RESOURCE_EXPIRATION_TIME);
        }

        // initialize collections from JSON files
        // TODO: FINIRE SALVATAGGIO SU FILESYSTEM
        // initializeCollections();
        // LabelsFunction.loadLabels();

        // default exception handling (simply logs exceptions)
        Spark.exception(Exception.class, (exception, request, response) -> {
            LOGGER.error("exception ", exception);

            Map<String, Object> attributes = new HashMap<>();
            Core.initializeRouteFrontEnd(request, response, attributes);
            response.redirect(RoutesUtils.contextPath(request) + Routes.ERROR_404);
        });

        Spark.notFound(HomeController.error_page);

        // clean-up useless slashes
        //Spark.before("*", Filters.removeTrailingSlashes);

        // check connections (when mongodb or redis aren't working)
        //Spark.before("*", addUserToRequest);

        // root default (when a static index.html doesn't exists)
        Spark.before("/", defaultPageRedirect);

        // errors
        get         (Routes.ERROR_404,                  HomeController.error_404, Core.engine);

        // login
        get         (Routes.BE_LOGIN,                   LoginController.be_login, Core.engine);
        post        (Routes.BE_LOGIN_DO,                LoginController.be_login_do);
        get         (Routes.BE_LOGOUT,                  LoginController.be_logout, Core.engine);
        
        // forgot
        get         (Routes.BE_FORGOT,                  LoginController.be_forgot, Core.engine);
        post        (Routes.BE_FORGOT_DO,               LoginController.be_forgot_do);

        // dashboard
        get         (Routes.BE_DASHBOARD,               DashboardController.be_dashboard, Core.engine);
        
        // manteinance / tables
        get         (Routes.BE_TABLE_COLLECTION,        TableController.be_table_collection, Core.engine);

        // settings
        /*get         (Routes.BE_SETTINGS_LANGUAGES,      SettingsController.be_settings_languages, Core.engine);
        post        (Routes.BE_SETTINGS_SAVE,           SettingsController.be_settings_save);*/

        get         (Routes.BE_SETTINGS_COMPANY,        CompanyController.be_company, Core.engine);
        post        (Routes.BE_SETTINGS_COMPANY_SAVE,   CompanyController.be_company_save);

        get         (Routes.BE_SETTINGS_SMTP_COLLECTION,SmtpController.be_smtp_collection, Core.engine);
        get         (Routes.BE_SETTINGS_SMTP,           SmtpController.be_smtp, Core.engine);
        get         (Routes.BE_SETTINGS_SMTP_DATA,      SmtpController.be_smtp_data);
        post        (Routes.BE_SETTINGS_SMTP_SAVE,      SmtpController.be_smtp_save);

        get         (Routes.BE_SETTINGS_USER_COLLECTION,SettingsUserController.be_settings_user_collection, Core.engine);
        get         (Routes.BE_SETTINGS_USER,           SettingsUserController.be_settings_user, Core.engine);
        get         (Routes.BE_SETTINGS_USER_DATA,      SettingsUserController.be_settings_user_data);
        post        (Routes.BE_SETTINGS_USER_SAVE,      SettingsUserController.be_settings_user_save);
        
        // user
        get         (Routes.BE_USER_COLLECTION,         UserController.be_user_collection, Core.engine);
        get         (Routes.BE_USER,                    UserController.be_user, Core.engine);
        get         (Routes.BE_USER_FORM,               UserController.be_user_form, Core.engine);
        get         (Routes.BE_USER_DATA,               UserController.be_user_data);
        post        (Routes.BE_USER_SAVE,               UserController.be_user_save);
        post        (Routes.BE_USER_OPERATE,            UserController.be_user_operate);
        get         (Routes.BE_USER_PERMISSIONS_MANAGER, PermissionController.be_user_permissions_manager, Core.engine);
        get         (Routes.BE_USER_PERMISSIONS_MANAGER_USERS, PermissionController.be_user_permissions_manager_users);
        get         (Routes.BE_USER_PERMISSIONS,        PermissionController.be_user_permissions);
        post        (Routes.BE_USER_PERMISSIONS_SAVE,   PermissionController.be_user_permissions_save);

        // contact
        get         (Routes.BE_CONTACT_COLLECTION,      ContactController.be_contact_collection, Core.engine);
        get         (Routes.BE_CONTACT,                 ContactController.be_contact, Core.engine);
        get         (Routes.BE_CONTACT_FORM,            ContactController.be_contact_form, Core.engine);
        get         (Routes.BE_CONTACT_DATA,            ContactController.be_contact_data);
        post        (Routes.BE_CONTACT_SAVE,            ContactController.be_contact_save);
        post        (Routes.BE_CONTACT_OPERATE,         ContactController.be_contact_operate);

        // country
        get         (Routes.BE_COUNTRY_COLLECTION,      CountryController.be_country_collection, Core.engine);
        get         (Routes.BE_COUNTRY,                 CountryController.be_country, Core.engine);
        get         (Routes.BE_COUNTRY_FORM,            CountryController.be_country_form, Core.engine);
        get         (Routes.BE_COUNTRY_DATA,            CountryController.be_country_data);
        post        (Routes.BE_COUNTRY_SAVE,            CountryController.be_country_save);
        post        (Routes.BE_COUNTRY_OPERATE,         CountryController.be_country_operate);

        // dealer
        get         (Routes.BE_DEALER_COLLECTION,       DealerController.be_dealer_collection, Core.engine);
        get         (Routes.BE_DEALER_VIEW,             DealerController.be_dealer_view, Core.engine);
        get         (Routes.BE_DEALER_FORM,             DealerController.be_dealer_form, Core.engine);
        get         (Routes.BE_DEALER_DATA,             DealerController.be_dealer_data);
        post        (Routes.BE_DEALER_SAVE,             DealerController.be_dealer_save);
        post        (Routes.BE_DEALER_OPERATE,          DealerController.be_dealer_operate);

        // insurance company
        get         (Routes.BE_INSURANCECOMPANY_COLLECTION, InsuranceCompanyController.be_insurancecompany_collection, Core.engine);
        get         (Routes.BE_INSURANCECOMPANY,        InsuranceCompanyController.be_insurancecompany, Core.engine);
        get         (Routes.BE_INSURANCECOMPANY_DATA,   InsuranceCompanyController.be_insurancecompany_data);
        post        (Routes.BE_INSURANCECOMPANY_SAVE,   InsuranceCompanyController.be_insurancecompany_save);
        post        (Routes.BE_INSURANCECOMPANY_OPERATE, InsuranceCompanyController.be_insurancecompany_operate);

        // warranty type
        get         (Routes.BE_WARRANTYTYPE_COLLECTION, WarrantyTypeController.be_warrantytype_collection, Core.engine);
        get         (Routes.BE_WARRANTYTYPE,            WarrantyTypeController.be_warrantytype, Core.engine);
        get         (Routes.BE_WARRANTYTYPE_DATA,       WarrantyTypeController.be_warrantytype_data);
        post        (Routes.BE_WARRANTYTYPE_SAVE,       WarrantyTypeController.be_warrantytype_save);
        post        (Routes.BE_WARRANTYTYPE_OPERATE,    WarrantyTypeController.be_warrantytype_operate);

        // insurance provenance type
        get         (Routes.BE_INSURANCEPROVENANCETYPE_COLLECTION, InsuranceProvenanceTypeController.be_insuranceprovenancetype_collection, Core.engine);
        get         (Routes.BE_INSURANCEPROVENANCETYPE, InsuranceProvenanceTypeController.be_insuranceprovenancetype, Core.engine);
        get         (Routes.BE_INSURANCEPROVENANCETYPE_DATA, InsuranceProvenanceTypeController.be_insuranceprovenancetype_data);
        post        (Routes.BE_INSURANCEPROVENANCETYPE_SAVE, InsuranceProvenanceTypeController.be_insuranceprovenancetype_save);
        post        (Routes.BE_INSURANCEPROVENANCETYPE_OPERATE, InsuranceProvenanceTypeController.be_insuranceprovenancetype_operate);

        // warranty
        get         (Routes.BE_WARRANTY_COLLECTION, WarrantyController.be_warranty_collection, Core.engine);
        get         (Routes.BE_WARRANTY,                WarrantyController.be_warranty, Core.engine);
        get         (Routes.BE_WARRANTY_DATA,           WarrantyController.be_warranty_data);
        post        (Routes.BE_WARRANTY_SAVE,           WarrantyController.be_warranty_save);
        post        (Routes.BE_WARRANTY_OPERATE,        WarrantyController.be_warranty_operate);
        get         (Routes.BE_WARRANTY_CRITERIA,       WarrantyController.be_warranty_criteria);

        // warranty criteria test
        get         (Routes.BE_WARRANTY_CRITERIA_TEST,  WarrantyCriteriaTestController.be_warranty_criteria_test, Core.engine);
        post        (Routes.BE_WARRANTY_CRITERIA_SEARCH, WarrantyCriteriaTestController.be_warranty_criteria_search);

        // warranty details
        get         (Routes.BE_WARRANTYDETAILS_COLLECTION, WarrantyDetailsController.be_warrantydetails_collection, Core.engine);
        get         (Routes.BE_WARRANTYDETAILS,         WarrantyDetailsController.be_warrantydetails, Core.engine);
        get         (Routes.BE_WARRANTYDETAILS_DATA,    WarrantyDetailsController.be_warrantydetails_data);
        post        (Routes.BE_WARRANTYDETAILS_SAVE,    WarrantyDetailsController.be_warrantydetails_save);
        post        (Routes.BE_WARRANTYDETAILS_OPERATE, WarrantyDetailsController.be_warrantydetails_operate);

        // channel
        get         (Routes.BE_CHANNEL_COLLECTION,      ChannelController.be_channel_collection, Core.engine);
        get         (Routes.BE_CHANNEL,                 ChannelController.be_channel, Core.engine);
        get         (Routes.BE_CHANNEL_DATA,            ChannelController.be_channel_data);
        post        (Routes.BE_CHANNEL_SAVE,            ChannelController.be_channel_save);
        post        (Routes.BE_CHANNEL_OPERATE,         ChannelController.be_channel_operate);

        // field translation
        get         (Routes.BE_FIELDTRANSLATION_COLLECTION, FieldTranslationController.be_fieldtranslation_collection, Core.engine);
        get         (Routes.BE_FIELDTRANSLATION,        FieldTranslationController.be_fieldtranslation, Core.engine);
        get         (Routes.BE_FIELDTRANSLATION_DATA,   FieldTranslationController.be_fieldtranslation_data);
        post        (Routes.BE_FIELDTRANSLATION_SAVE,   FieldTranslationController.be_fieldtranslation_save);
        post        (Routes.BE_FIELDTRANSLATION_OPERATE, FieldTranslationController.be_fieldtranslation_operate);
        post        (Routes.BE_FIELDTRANSLATION_REGENERATE, FieldTranslationController.be_fieldtranslation_regenerate);

        // mailtemplate
        get         (Routes.BE_MAILTEMPLATE_COLLECTION, MailtemplateController.be_mailtemplate_collection, Core.engine);
        get         (Routes.BE_MAILTEMPLATE,            MailtemplateController.be_mailtemplate, Core.engine);
        get         (Routes.BE_MAILTEMPLATE_DATA,       MailtemplateController.be_mailtemplate_data);
        post        (Routes.BE_MAILTEMPLATE_SAVE,       MailtemplateController.be_mailtemplate_save);

        // smtp
        post        (Routes.BE_SEND_MAIL,              SmtpController.send_mail);

        // import
        get         (Routes.BE_IMPORT,                 ImportController.be_import, Core.engine);
        post        (Routes.BE_IMPORT_PROCESS,         ImportController.be_import_process);

        // files
        get         (Routes.BE_IMAGE,               ImageController.image);
        post        (Routes.BE_IMAGE_SAVE,          ImageController.image_save);
        get         (Routes.BE_IMAGE_BLOB,          ImageController.image_blob);
        get         (Routes.BE_FILE,                FileController.file);

        // data endpoints for remote select population
        get         (Routes.BE_DATA_COUNTRIES,      DataController.be_data_countries);
        get         (Routes.BE_DATA_CITIES,         DataController.be_data_cities);
        get         (Routes.BE_DATA_PROVINCES,      DataController.be_data_provinces);

        // logs
        get         (Routes.BE_ENTITY_LOGS,         LogController.be_entity_logs);
    }

    public static Filter defaultPageRedirect = (Request request, Response response) -> {
        response.redirect(RoutesUtils.contextPath(request) + "/login");
        Spark.halt();
    };

    @Override
    public void destroy() {
        Core.destroy();
    }

    private static void get(String path, Route route) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            Spark.get(localizedPath, route);
        }
    }

    private static void get(String path, TemplateViewRoute route, TemplateEngine engine) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.get(localizedPath, route, engine);
                }
            }
        }
    }

    private static void post(String path, Route route) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.post(localizedPath, route);
                }
            }
        }
    }

    private static void post(String path, TemplateViewRoute route, TemplateEngine engine) {
        List<String> localizedPaths = Routes.getLocalizedPath(path);
        for (String localizedPath : localizedPaths) {
            if (StringUtils.isNotBlank(localizedPath)) {
                if (reloadApplication) {
                    Spark.unmap(localizedPath);
                } else {
                    Spark.post(localizedPath, route, engine);
                }
            }
        }
    }

}

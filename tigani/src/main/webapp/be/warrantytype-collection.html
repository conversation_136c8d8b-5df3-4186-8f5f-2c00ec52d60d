{% extends "be/include/base.html" %}

{% set page = 'WARRANTYTYPE' %}

{% block extrahead %}
<title>Tipi di Garanzia</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/warrantytype-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_WARRANTYTYPE_DATA', '{{ routes("BE_WARRANTYTYPE_DATA") }}');
addRoute('BE_WARRANTYTYPE_OPERATE', '{{ routes("BE_WARRANTYTYPE_OPERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Tipi di Garanzia</h5>
            <div class="ms-sm-auto my-sm-auto">
                <a href="{{ routes('BE_WARRANTYTYPE') }}" class="btn btn-primary">
                    <i class="ph-plus me-2"></i>
                    Aggiungi Tipo Garanzia
                </a>
            </div>
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Selez.</th>
                    <th>Nome</th>
                    <th>Codice</th>
                    <th>Icona</th>
                    <th>Creazione</th>
                    <th>Ultima modifica</th>
                    <th class="text-center" style="width: 120px;">Azioni</th>
                    <th></th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}

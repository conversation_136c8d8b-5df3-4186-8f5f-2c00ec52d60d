<!DOCTYPE html>
<html lang="it" dir="ltr" class="relative min-h-full">
    <head>

        <!-- Meta -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <!-- Favicon -->
        <link rel="shortcut icon" href="">

        <!-- Font -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

        <!-- CSS -->
        <link href="{{ contextPath }}/css/main.min.css" rel="stylesheet" type="text/css">
        <link href="{{ contextPath }}/css/custom.css" rel="stylesheet" type="text/css">

        <!-- Libs -->
        {% include "be/include/snippets/plugins/jquery.html" %}
        {% include "be/include/snippets/plugins/jquery-confirm.html" %}
        {% include "be/include/snippets/plugins/jquery-blockui.html" %}
        {% include "be/include/snippets/plugins/moment.html" %}
        {% include "be/include/snippets/plugins/toastify.html" %} <!-- da togliere sulle singola -->
        {% include "be/include/snippets/plugins/jquery-blockui.html" %}

        <!-- Theme Check and Update -->
        <script>
          const html = document.querySelector('html');
          const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
          const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

          if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
          else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
          else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
          else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>

        {% block extrahead %}{% endblock %}
    </head>

    <body class="bg-gray-50 dark:bg-neutral-900">

        <!-- ========== HEADER ========== -->
        {% include "be/include/snippets/preline-header.html" %}
        <!-- ========== END HEADER ========== -->

        <!-- ========== MAIN CONTENT ========== -->
        <main id="content" class="pt-15">
          <!-- Container -->
          <div class="max-w-full mx-auto">
            <!-- Content -->

                <!-- ========== MAIN SIDEBAR ========== -->
                {% include "be/include/snippets/preline-sidebar.html" %}
                <!-- ========== END MAIN SIDEBAR ========== -->

                <!-- ========== MAIN CONTENT ========== -->
                {% block content %}{% endblock %}
                <!-- ========== END MAIN CONTENT ========== -->
        </main>

        <!-- JS -->

        <!-- Preline -->
        <script src="{{ contextPath }}/js/preline.min.js"></script>

        <!-- Libs -->
        {% include "be/include/snippets/plugins/lodash.html" %}

        <!-- Custom -->
        <script src="{{ contextPath }}/js/utils/custom.js"></script>

        <!-- Log -->
        <script type="text/javascript">
            addRoute('BE_ENTITY_LOGS', '{{ routes("BE_ENTITY_LOGS") }}');

            // Add user permissions to JavaScript
            {% if user is not empty and user.permissions is not empty %}
            {% for entry in user.permissions %}
            {% set permissionCode = entry.key %}
            {% set permissionTypes = entry.value %}
            addUserPermission('{{ permissionCode }}', '{{ permissionTypes | join("-_-") }}');
            {% endfor %}
            {% endif %}

            {% if user is not empty and user.profileType == 'system' %}
            addUserPermission('GLOBAL', 'view-_-create-_-edit-_-delete');
            {% endif %}
        </script>
        <script src="{{ contextPath }}/js/utils/log.js"></script>

        {% block pagescript %}{% endblock %}

    </body>

</html>
{% extends "be/include/base.html" %}

{% set page = 'IMPORT' %}
{% set title = 'Importazione Dati' %}

{% block extrahead %}

<title>{{ title }}</title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<!-- specific script-->

<link href="https://anubi.us/static/lib/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Page script -->
<script src="https://anubi.us/static/lib/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/import.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_IMPORT', '{{ routes("BE_IMPORT") }}');
    addRoute('BE_IMPORT_PROCESS', '{{ routes("BE_IMPORT_PROCESS") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Importazione Dati da Excel</h5>
        </div>

        <div class="card-body">
            <form id="import-form" class="form-validate" method="POST" action="{{ routes('BE_IMPORT_PROCESS') }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Tipo di Importazione: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <select name="importType" class="form-select" required>
                            <option value="">Seleziona il tipo di dati da importare</option>
                            <option value="comuni">Comuni (Cities)</option>
                            <option value="stati">Stati (Countries)</option>
                            <option value="marche">Marche Veicoli (Vehicle Brands)</option>
                            <option value="modelli">Modelli Veicoli (Vehicle Models)</option>
                            <option value="allestimenti">Allestimenti Veicoli (Vehicle Model Setups)</option>
                        </select>
                        <div class="form-text text-muted">Seleziona il tipo di dati che vuoi importare dal file Excel.</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">File Excel: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <div id="uploader-text" style="display: none;">Trascina qui il file Excel</div>
                        <div class="text-center">
                            <input type="file" name="excelFile" accept=".xlsx,.xls" required data-maxfilessize="10485760">
                        </div>
                        <div class="form-text text-muted">
                            Carica il file Excel (.xlsx) contenente i dati da importare. Dimensione massima: 10MB.
                        </div>
                    </div>
                </div>

                <!-- Import type specific information -->
                <div id="comuni-info" class="row mb-3" style="display: none;">
                    <div class="col-lg-3"></div>
                    <div class="col-lg-9">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Formato per Comuni:</h6>
                            <p class="mb-0">Il file Excel deve contenere le seguenti colonne nell'ordine specificato:</p>
                            <ol class="mb-0 mt-2">
                                <li><strong>codice_istat</strong> - Codice ISTAT</li>
                                <li><strong>denominazione_ita</strong> - Nome del comune</li>
                                <li><strong>cap</strong> - Codice Avviamento Postale</li>
                                <li><strong>sigla_provincia</strong> - Sigla della provincia</li>
                                <li><strong>denominazione_provincia</strong> - Nome della provincia</li>
                                <li><strong>denominazione_regione</strong> - Nome della regione</li>
                                <li><strong>codice_belfiore</strong> - Codice Belfiore</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div id="stati-info" class="row mb-3" style="display: none;">
                    <div class="col-lg-3"></div>
                    <div class="col-lg-9">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Formato per Stati:</h6>
                            <p class="mb-0">Il file Excel deve contenere le seguenti colonne nell'ordine specificato:</p>
                            <ol class="mb-0 mt-2">
                                <li><strong>sigla_nazione</strong> - Codice del paese</li>
                                <li><strong>codice_belfiore</strong> - Codice Belfiore</li>
                                <li><strong>denominazione_nazione</strong> - Nome del paese</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div id="marche-info" class="row mb-3" style="display: none;">
                    <div class="col-lg-3"></div>
                    <div class="col-lg-9">
                        <div class="alert alert-success">
                            <h6 class="alert-heading">Importazione Marche Veicoli:</h6>
                            <p class="mb-2">Questa importazione recupera automaticamente le marche dei veicoli dall'API Italiana Assicurazioni.</p>
                            <ul class="mb-0">
                                <li><strong>Nessun file richiesto</strong> - I dati vengono scaricati direttamente dall'API</li>
                                <li><strong>Parametri utilizzati:</strong> Canale, Codice Compagnia, Codice Agenzia, Codice Agente, Classe Veicolo</li>
                                <li><strong>Duplicati:</strong> Le marche esistenti vengono aggiornate automaticamente</li>
                                <li><strong>Chiave identificativa:</strong> Codice marca</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div id="modelli-info" class="row mb-3" style="display: none;">
                    <div class="col-lg-3"></div>
                    <div class="col-lg-9">
                        <div class="alert alert-success">
                            <h6 class="alert-heading">Importazione Modelli Veicoli:</h6>
                            <p class="mb-2">Questa importazione recupera automaticamente i modelli dei veicoli dall'API Italiana Assicurazioni per tutte le marche presenti nel database.</p>
                            <ul class="mb-0">
                                <li><strong>Nessun file richiesto</strong> - I dati vengono scaricati direttamente dall'API</li>
                                <li><strong>Prerequisito:</strong> Le marche devono essere importate prima dei modelli</li>
                                <li><strong>Relazione:</strong> Ogni modello viene collegato alla sua marca di appartenenza</li>
                                <li><strong>Data immatricolazione:</strong> Viene utilizzata la data odierna</li>
                                <li><strong>Duplicati:</strong> I modelli esistenti vengono aggiornati automaticamente</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div id="allestimenti-info" class="row mb-3" style="display: none;">
                    <div class="col-lg-3"></div>
                    <div class="col-lg-9">
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">Importazione Allestimenti Veicoli:</h6>
                            <p class="mb-2">Questa importazione recupera automaticamente gli allestimenti/configurazioni dei veicoli dall'API Italiana Assicurazioni per tutte le combinazioni marca-modello presenti nel database.</p>
                            <ul class="mb-0">
                                <li><strong>Nessun file richiesto</strong> - I dati vengono scaricati direttamente dall'API</li>
                                <li><strong>Prerequisiti:</strong> Marche e modelli devono essere importati prima degli allestimenti</li>
                                <li><strong>Relazione:</strong> Ogni allestimento viene collegato alla sua marca e modello di appartenenza</li>
                                <li><strong>Parametri API:</strong> Utilizza codice marca e codice modello per ogni combinazione</li>
                                <li><strong>Data immatricolazione:</strong> Viene utilizzata la data odierna</li>
                                <li><strong>Duplicati:</strong> Gli allestimenti esistenti vengono aggiornati automaticamente</li>
                                <li><strong>Processo:</strong> Può richiedere più tempo in quanto elabora tutte le combinazioni marca-modello</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Progress bar -->
                <div id="import-progress" class="row mb-3" style="display: none;">
                    <div class="col-lg-3"></div>
                    <div class="col-lg-9">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="form-text text-muted mt-2">
                            <span id="progress-text">Importazione in corso...</span>
                        </div>
                    </div>
                </div>

                <!-- Results -->
                <div id="import-results" class="row mb-3" style="display: none;">
                    <div class="col-lg-3"></div>
                    <div class="col-lg-9">
                        <div id="results-content"></div>
                    </div>
                </div>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary" id="import-btn">
                        <i class="ph-upload me-2"></i>
                        Avvia Importazione
                    </button>
                    <button type="button" class="btn btn-light" id="reset-btn" style="display: none;">
                        <i class="ph-arrow-clockwise me-2"></i>
                        Nuova Importazione
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->

    <!-- Instructions -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Istruzioni per l'Importazione</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Comuni (Cities)</h6>
                    <p>Per importare i dati dei comuni italiani:</p>
                    <ul>
                        <li>Utilizza il file <code>gi_comuni_cap.xlsx</code> come riferimento</li>
                        <li>Il sistema aggiornerà i comuni esistenti o ne creerà di nuovi</li>
                        <li>La chiave di identificazione è il <strong>codice_istat</strong></li>
                        <li>Il codice paese sarà automaticamente impostato su "IT"</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Stati (Countries)</h6>
                    <p>Per importare i dati degli stati:</p>
                    <ul>
                        <li>Utilizza il file <code>gi_nazioni.xlsx</code> come riferimento</li>
                        <li>Il sistema aggiornerà gli stati esistenti o ne creerà di nuovi</li>
                        <li>La chiave di identificazione è la <strong>sigla_nazione</strong></li>
                        <li>Assicurati che i codici paese siano univoci</li>
                    </ul>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <h6>Marche Veicoli (Vehicle Brands)</h6>
                    <p>Per importare le marche dei veicoli dall'API Italiana:</p>
                    <ul>
                        <li><strong>Nessun file Excel richiesto</strong></li>
                        <li>I dati vengono scaricati automaticamente dall'API</li>
                        <li>Utilizza i parametri configurati in <code>Defaults.java</code></li>
                        <li>La chiave di identificazione è il <strong>codice marca</strong></li>
                        <li>Prerequisito per l'importazione dei modelli</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Modelli Veicoli (Vehicle Models)</h6>
                    <p>Per importare i modelli dei veicoli dall'API Italiana:</p>
                    <ul>
                        <li><strong>Nessun file Excel richiesto</strong></li>
                        <li>Richiede che le marche siano già state importate</li>
                        <li>Ogni modello viene collegato alla sua marca</li>
                        <li>Utilizza la data odierna come data di immatricolazione</li>
                        <li>La chiave di identificazione è <strong>codice modello + marca</strong></li>
                    </ul>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h6>Allestimenti Veicoli (Vehicle Model Setups)</h6>
                    <p>Per importare gli allestimenti/configurazioni dei veicoli dall'API Italiana:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <ul>
                                <li><strong>Nessun file Excel richiesto</strong></li>
                                <li>Richiede che marche e modelli siano già stati importati</li>
                                <li>Ogni allestimento viene collegato alla sua marca e modello</li>
                                <li>Utilizza codice marca e codice modello per le chiamate API</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul>
                                <li>Utilizza la data odierna come data di immatricolazione</li>
                                <li>La chiave di identificazione è <strong>codice allestimento + marca + modello</strong></li>
                                <li><strong>Attenzione:</strong> Processo più lungo in quanto elabora tutte le combinazioni</li>
                                <li>Ordine consigliato: Marche → Modelli → Allestimenti</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-warning mt-3">
                <h6 class="alert-heading">Attenzione:</h6>
                <ul class="mb-0">
                    <li>L'importazione può richiedere alcuni minuti per file di grandi dimensioni</li>
                    <li>I dati esistenti verranno aggiornati se trovata una corrispondenza</li>
                    <li>Assicurati che il formato del file corrisponda esattamente a quello richiesto</li>
                    <li>La prima riga del file Excel deve contenere le intestazioni delle colonne</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- /instructions -->

</div>
<!-- /content area -->

{% endblock %}

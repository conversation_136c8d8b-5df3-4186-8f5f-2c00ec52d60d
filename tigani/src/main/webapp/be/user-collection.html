{% extends "be/include/preline-base.html" %}

{% block extrahead %}

    {% set menu = 'MAINTENANCE' %}
    {% set submenu = 'USER_COLLECTION' %}

    <title>Manutenzione / Utenti</title>

    <!-- Page Libs -->
    {% include "be/include/snippets/plugins/datatable.html" %}
    {% include "be/include/snippets/plugins/daterangepicker.html" %}
    {% include "be/include/snippets/plugins/filepond.html" %}
    {% include "be/include/snippets/plugins/toastify.html" %}
    {% include "be/include/snippets/plugins/validate.html" %}
    {% include "be/include/snippets/plugins/maxlength.html" %}

{% endblock %}

{% block content %}

<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <!-- Card -->
    <div class="bg-white border border-gray-200 shadow-xs rounded-xl dark:bg-neutral-900 dark:border-neutral-700">
        <!-- Header -->
        <div class="py-3 px-5 flex flex-wrap justify-between items-center gap-2 border-b border-gray-200 dark:border-neutral-700">
            <!-- Title -->
            <div class="flex flex-wrap items-center gap-2">
                <h2 class="font-medium text-gray-800 dark:text-neutral-200">
                    Utenti
                </h2>
            </div>
            <!-- End Title -->
            <!-- Actions -->
            <div class="flex flex-wrap items-center gap-2">
                {% if user.hasPermission('USER_MANAGEMENT', 'create') %}
                <button type="button" id="create-user-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-hidden focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                    Nuovo Utente
                </button>
                {% endif %}
            </div>
            <!-- End Actions -->
        </div>
        <!-- End Header -->
                
        <!-- DataTable Content -->        
        <div class="flex flex-col">
            <div id="user-datatable-container">
                <!-- DataTable Header -->
                <div class="flex flex-wrap items-center gap-2 p-5">                    
                    <!-- Search -->                    
                    {% include "be/include/snippets/tables/search.html" %}                                
                    <!-- Search -->
                    <div class="flex-1 flex items-center justify-start space-x-2 xl:justify-end">                        
                        <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                            <!-- Date Range Filter -->
                            {% include "be/include/snippets/tables/daterange.html" %}
                            <!-- End Date Range Filter -->

                            <!-- Filters -->
                            {% if user.hasPermission('USER_MANAGEMENT', 'view') %}
                                {% include "be/include/snippets/tables/filters.html" %}                                
                            {% endif %}
                            <!-- End Filters -->
                            
                            <!-- Export -->
                            {% if user.hasPermission('USER_MANAGEMENT', 'view') %}
                                {% include "be/include/snippets/tables/export.html" %}
                            {% endif %}
                            <!-- End Export -->                            
                        </div>
                    </div>
                </div>
                <!-- End DataTable Header -->
                                
                <!-- Table -->
                <div class="overflow-x-auto">
                    <div class="min-w-full inline-block align-middle">
                        <div class="overflow-hidden">
                            <table class="min-w-full">
                                <thead class="border-y border-gray-200 dark:border-neutral-700 bg-gray-50 dark:bg-neutral-800">
                                <tr>
                                    <th scope="col" class="!py-1 px-5 --exclude-from-ordering">
                                        <div class="flex items-center h-5">
                                            <input id="hs-table-search-checkbox-all" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800 cursor-pointer">
                                            <label for="hs-table-search-checkbox-all" class="sr-only">Checkbox</label>
                                        </div>
                                    </th>                                    
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Nome
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Email
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Telefono
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Profilo
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Data Creazione
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 !px-0 group text-start font-normal focus:outline-hidden">
                                        <div class="py-1 px-2.5 inline-flex items-center border border-transparent text-sm font-medium text-gray-800 rounded-md hover:border-gray-200 dark:text-neutral-200 dark:hover:border-neutral-700 cursor-pointer">
                                            Data Modifica
                                            <svg class="size-3.5 ms-1 -me-0.5 text-gray-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path class="hs-datatable-ordering-desc:text-blue-600 dark:hs-datatable-ordering-desc:text-blue-500" d="m7 15 5 5 5-5"></path>
                                                <path class="hs-datatable-ordering-asc:text-blue-600 dark:hs-datatable-ordering-asc:text-blue-500" d="m7 9 5-5 5 5"></path>
                                            </svg>
                                        </div>
                                    </th>
                                    <th scope="col" class="!py-1 px-5 text-sm font-medium text-gray-800 --exclude-from-ordering dark:text-neutral-200">Azioni</th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-neutral-700">
                                <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!--  End Table -->
                
                <!-- Table Footer -->
                <div class="flex flex-wrap justify-between items-center gap-2 p-4">
                    <div class="flex items-center gap-x-1 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-400" data-hs-datatable-info="">
                        <span>Visualizzo</span>

                        <!-- Select -->
                        <select class="hidden" data-hs-select='{
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-title></span></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                "dropdownClasses": "mt-2 z-50 w-20 max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-md focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                                "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                }' data-hs-datatable-page-entities="">
                            <option value="10" selected="">10</option>
                            <option value="15">15</option>
                            <option value="20">20</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                        <!-- End Select -->

                        <span>da</span>
                        <span data-hs-datatable-info-from=""></span>
                        <span>a</span>
                        <span data-hs-datatable-info-to=""></span>
                        <span>di</span>
                        <span data-hs-datatable-info-length=""></span>
                        <span>utenti</span>
                    </div>       
                    <div class="inline-flex items-center gap-1" data-hs-datatable-paging="">                        
                        
                        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-datatable-paging-prev="">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                            Indietro
                        </button>                        
                        <div class="flex items-center space-x-1 [&>button]:py-2 [&>button]:px-3 [&>button]:text-sm [&>button]:font-medium [&>button]:rounded-lg [&>button]:border [&>button]:border-gray-200 [&>button]:bg-white [&>button]:text-gray-800 [&>button]:shadow-2xs [&>button]:hover:bg-gray-50 [&>button]:focus:outline-hidden [&>button]:focus:bg-gray-50 [&>button]:dark:bg-neutral-800 [&>button]:dark:border-neutral-700 [&>button]:dark:text-white [&>button]:dark:hover:bg-neutral-700 [&>button]:dark:focus:bg-neutral-700 [&>.active]:bg-blue-500 [&>.active]:text-white [&>.active]:border-blue-500" data-hs-datatable-paging-pages=""></div>
                        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-datatable-paging-next="">
                            Avanti
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
                        </button>                                                                       
                    </div>
                </div>   
                <!-- End Table Footer -->
            </div>
        </div>
        <!-- End DataTable Content -->
    </div>
    <!-- End Card -->
</div>
<!-- End Container -->

<!-- Fixed Bottom Bulk Actions - Preline Inbox Demo Style -->
<div id="bulk-actions-container" class="bulk-action-container hidden fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
    <div class="flex items-center gap-x-3 py-3 px-5 bg-gray-800 border border-gray-700 rounded-xl shadow-2xl dark:bg-gray-900 dark:border-gray-600 backdrop-blur-sm">
        <!-- Selection Counter -->
        <div class="flex items-center gap-x-2">
            <span id="selected-count" class="text-sm font-medium text-white">0</span>
            <span class="text-sm text-gray-300">Selezionati</span>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center gap-x-2">
            <!-- Confirm Selected -->
            {% if user.hasPermission('USER_MANAGEMENT', 'edit') %}
            <button type="button" class="inline-flex items-center gap-x-1.5 py-2 px-3 text-xs font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none transition-colors duration-200" onclick="UserCollection.confirmSelectedRows(); return false;">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                Conferma
            </button>
            {% endif %}

            <!-- Archive Selected -->
            {% if user.hasPermission('USER_MANAGEMENT', 'edit') %}
            <button type="button" class="inline-flex items-center gap-x-1.5 py-2 px-3 text-xs font-medium rounded-lg border border-gray-600 bg-gray-700 text-white hover:bg-gray-600 focus:outline-none focus:bg-gray-600 disabled:opacity-50 disabled:pointer-events-none dark:border-gray-500 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:bg-gray-700 transition-colors duration-200" onclick="UserCollection.archiveSelectedRows(); return false;">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>
                Archivia
            </button>
            {% endif %}

            <!-- Delete Selected -->
            {% if user.hasPermission('USER_MANAGEMENT', 'delete') %}
            <button type="button" class="inline-flex items-center gap-x-1.5 py-2 px-3 text-xs font-medium rounded-lg border border-transparent bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:bg-red-700 disabled:opacity-50 disabled:pointer-events-none transition-colors duration-200" onclick="UserCollection.deleteSelectedRows(); return false;">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                Elimina
            </button>
            {% endif %}

            <!-- Close Button -->
            <button type="button" class="inline-flex items-center justify-center size-9 text-sm font-semibold rounded-lg border border-transparent text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:text-white focus:bg-gray-700 disabled:opacity-50 disabled:pointer-events-none transition-colors duration-200" onclick="UserCollection.clearSelection(); return false;" aria-label="Close">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 6-12 12"/><path d="m6 6 12 12"/></svg>
            </button>
        </div>
    </div>
</div>

<!-- Table Filters Offcanvas -->
<div id="table-filters" class="hs-overlay hs-overlay-open:translate-x-0 hidden translate-x-full fixed top-0 end-0 transition-all duration-300 transform size-full sm:w-100 z-80 flex flex-col bg-white dark:bg-neutral-800" role="dialog" tabindex="-1" aria-labelledby="hs-pro-shflo-label">
    <!-- Header -->
    <div class="py-3 px-6 flex justify-between items-center border-b border-gray-200 dark:border-neutral-700">
        <h3 id="hs-pro-shflo-label" class="font-medium text-gray-800 dark:text-neutral-200">
            Filtra
        </h3>
        <button type="button" class="py-1.5 px-2 inline-flex justify-center items-center gap-x-1 rounded-full border border-gray-200 text-xs text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:hover:bg-neutral-700 dark:text-neutral-200 dark:focus:bg-neutral-700" aria-label="Close" data-hs-overlay="#table-filters">
            <span class="hidden lg:block">Esc</span>
            <span class="block lg:hidden">Chiudi</span>
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
        </button>
    </div>
    <!-- End Header -->

    <!-- Body -->
    <div class="bg-gray-100 h-full overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-800">
        <div class="p-2 space-y-2">
            <!-- Archived Filter Card -->
            <div class="p-4 bg-white rounded-lg shadow-2xs dark:bg-neutral-900">
                <div class="mb-3">
                    <span class="font-medium text-sm text-gray-800 dark:text-neutral-200">Opzioni di visualizzazione</span>
                </div>
                
                <!-- Archived -->
                <div class="flex items-center">
                    <label for="user_archived" class="p-2 group w-full inline-flex items-center cursor-pointer text-sm rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800">
                        <input type="checkbox" class="shrink-0 size-4.5 border-gray-300 rounded-sm text-blue-600 checked:border-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-500 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" id="user_archived" onchange="TableCollection.reloadTable(this.checked)">
                            <span class="ms-2 text-gray-800 dark:text-neutral-400">Mostra archiviati</span>                        
                    </label>
                </div>
                <!-- End Archived Checkbox -->
                               
            </div>
            <!-- End Archived Filter Card -->

        </div>
    </div>
    <!-- End Body -->

    <!-- Footer -->
    <div class="p-6 border-t border-gray-200 dark:border-neutral-700">
        <div class="flex items-center gap-x-2">
            <button type="button" class="py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-blue-700" data-hs-overlay="#table-filters">
                Chiudi
            </button>
        </div>
    </div>
    <!-- End Footer -->
</div>
<!-- End Table Filters Offcanvas -->
{% endblock %}

{% block pagescript %}

    <!-- Reload -->
    <script class="reload-script-on-load">
        addRoute('BE_USER_DATA', '{{ routes("BE_USER_DATA") }}');
        addRoute('BE_USER_OPERATE', '{{ routes("BE_USER_OPERATE") }}');
        addRoute('BE_USER_FORM', '{{ routes("BE_USER_FORM") }}');
        addRoute('BE_USER_SAVE', '{{ routes("BE_USER_SAVE") }}');
        addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    </script>

    <!-- Page Scripts -->
    <script src="{{ contextPath }}/js/pages/user-collection.js?{{ buildNumber }}"></script>
    <script src="{{ contextPath }}/js/pages/user-form.js?{{ buildNumber }}"></script>
{% endblock %}
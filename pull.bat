@echo off
rem -- ------------------------------------------------------------------
rem -- ------------------------------------------------------------------
rem -- the following git commands execute a pull from remote repository
rem -- ------------------------------------------------------------------
rem -- ------------------------------------------------------------------


rem -- first of all, you need a correctly configured git client to procede
rem -- if you're having troubles with git, please open the specifically
rem -- designed git-cmd.exe shell


rem -- check if current folder contains a git repository
if not exist ".\.git\*" goto :norep


rem -- pull others changes
echo pulling repository
git pull origin main

goto :end


rem -- errors
:norep
echo sorry, no rep folder found
goto :end


rem -- end
:end
pause

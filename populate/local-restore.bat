@echo off
REM === CONFIGURAZIONE VARIABILI FISSE ===
set REMOTE_HOST=************
set REMOTE_PORT=22
set REMOTE_USER=root
set REMOTE_PASS=J19iMnWDkF6
set MONGO_REMOTE_URI="mongodb://localhost:27017"
set MONGO_LOCAL_URI="mongodb://localhost:27017"

REM === PARAMETRO DATABASE ===
set DBNAME=tigani

if "%DBNAME%"=="" (
    echo Inserisci il nome del database come parametro!
    echo Esempio: mongorestore.bat nome_db
    exit /b 1
)

REM === ESECUZIONE COMANDO ===
c:\opt\putty\PLINK.EXE -ssh -P %REMOTE_PORT% -l %REMOTE_USER% -pw %REMOTE_PASS% %REMOTE_HOST% "mongodump --archive --gzip --db=%DBNAME% --uri=%MONGO_REMOTE_URI%" | "C:\opt\mongodb\server\bin\mongorestore" --drop --gzip --archive --nsInclude="%DBNAME%.*" --uri="%MONGO_LOCAL_URI%"